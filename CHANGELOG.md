# 变更日志

本文件记录了菜篮子智能比价工具的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
版本控制遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 计划新增
- 支持更多Excel格式
- 添加数据导入模板
- 增加批量处理功能

### 计划修改
- 优化大文件处理性能
- 改进用户界面设计

## [1.0.0] - 2024-12-19

### 新增
- 🎉 首次发布菜篮子智能比价工具
- 📊 Excel文件解析和比价分析功能
- 🧠 智能日期匹配算法，支持优先级匹配（25日→26日→27日→28日→29日）
- 📈 可视化比价结果展示，包含价格差异分析
- 📤 数据导出功能，支持Excel和CSV格式
- 🌐 内部网络部署支持，无需外网连接
- 🔍 智能部署检查工具
- 📋 完整的部署文档和用户指南
- 🛠️ 多种启动方式（本地服务器、直接访问、Web服务器）
- 📱 响应式设计，支持不同屏幕尺寸

### 技术特性
- 纯前端实现，基于HTML5 + JavaScript + CSS3
- 使用SheetJS库进行Excel文件处理
- 支持主流浏览器（Chrome 60+, Firefox 55+, Safari 12+, Edge 79+）
- 本地数据处理，保证数据安全性
- 模块化代码结构，便于维护和扩展

### 核心算法
- 日期匹配优先级算法
- 商品名称智能匹配
- 价格差异计算和分析
- 数据验证和错误处理

### 部署工具
- 自动化部署检查脚本
- 多语言启动脚本（中英文）
- 环境兼容性检测
- 依赖库管理工具

### 文档和支持
- 详细的用户使用手册
- 完整的部署指南
- 故障排除文档
- 版本管理指南

### 安全特性
- 客户端数据处理，不上传到服务器
- 支持内网环境，数据不外泄
- 文件格式验证，防止恶意文件
- 错误处理机制，防止系统崩溃

---

## 版本说明

### 版本号格式
- **主版本号**：重大功能变更或架构调整
- **次版本号**：新功能添加，保持向后兼容
- **修订号**：Bug修复和小幅优化

### 变更类型
- **新增**：新功能
- **修改**：现有功能的变更
- **弃用**：即将移除的功能
- **移除**：已移除的功能
- **修复**：Bug修复
- **安全**：安全相关的修复

### 发布类型
- **stable**：稳定版，适合生产环境
- **beta**：测试版，功能基本完整
- **alpha**：开发版，内部测试使用

---

## 贡献指南

如需提交变更或报告问题：

1. 详细描述变更内容
2. 说明变更原因和影响
3. 提供测试结果
4. 更新相关文档

---

**最后更新：2024-12-19**
