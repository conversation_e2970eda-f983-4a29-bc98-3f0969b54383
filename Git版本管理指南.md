# 菜篮子智能比价工具 - Git版本管理指南

## 🎯 为什么使用Git？

相比于我之前创建的简单脚本，Git提供了更专业的版本管理功能：

### Git的优势
- ✅ **完整的版本历史** - 每次提交都有完整记录
- ✅ **分支管理** - 支持并行开发和功能分支
- ✅ **回滚能力** - 可以轻松回退到任何历史版本
- ✅ **协作支持** - 多人协作开发
- ✅ **标签管理** - 标记重要版本节点
- ✅ **差异对比** - 清晰显示文件变更
- ✅ **备份安全** - 分布式存储，数据安全

### 与简单脚本的对比

| 功能 | 简单脚本 | Git |
|------|----------|-----|
| 版本历史 | 手动记录 | 自动记录 |
| 文件对比 | 无 | 内置diff |
| 回滚能力 | 手动备份 | 一键回滚 |
| 分支管理 | 无 | 完整支持 |
| 协作开发 | 困难 | 原生支持 |
| 标签管理 | 手动 | 自动化 |

## 🚀 Git版本管理设置

### 1. 初始化Git仓库

```bash
# 在项目根目录执行
cd D:\菜篮子智能比价工具

# 初始化Git仓库
git init

# 配置用户信息
git config user.name "您的姓名"
git config user.email "您的邮箱"

# 查看状态
git status
```

### 2. 创建.gitignore文件

```bash
# 创建.gitignore文件，排除不需要版本控制的文件
```

### 3. 首次提交

```bash
# 添加所有文件到暂存区
git add .

# 创建首次提交
git commit -m "feat: 初始版本 v1.0.0 - 菜篮子智能比价工具"

# 创建版本标签
git tag -a v1.0.0 -m "Release version 1.0.0"
```

## 📋 Git版本管理工作流

### 标准工作流程

```
开发 → 暂存 → 提交 → 标签 → 推送
 ↓      ↓      ↓      ↓      ↓
编码   git add git commit git tag git push
```

### 分支策略

```
main (主分支)
├── develop (开发分支)
├── feature/新功能名 (功能分支)
├── hotfix/bug修复名 (热修复分支)
└── release/版本号 (发布分支)
```

## 🔧 常用Git命令

### 日常开发命令

```bash
# 查看状态
git status

# 查看变更
git diff

# 添加文件到暂存区
git add 文件名
git add .  # 添加所有文件

# 提交变更
git commit -m "提交信息"

# 查看提交历史
git log --oneline

# 查看所有标签
git tag
```

### 版本管理命令

```bash
# 创建版本标签
git tag -a v1.0.1 -m "Release version 1.0.1"

# 查看标签信息
git show v1.0.1

# 检出特定版本
git checkout v1.0.1

# 回到最新版本
git checkout main

# 删除标签
git tag -d v1.0.1
```

### 分支管理命令

```bash
# 创建并切换到新分支
git checkout -b feature/新功能

# 切换分支
git checkout main

# 合并分支
git merge feature/新功能

# 删除分支
git branch -d feature/新功能

# 查看所有分支
git branch -a
```

## 📝 提交信息规范

### 提交信息格式

```
<类型>(<范围>): <描述>

[可选的正文]

[可选的脚注]
```

### 提交类型

- **feat**: 新功能
- **fix**: Bug修复
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 示例

```bash
# 新功能
git commit -m "feat: 添加批量数据处理功能"

# Bug修复
git commit -m "fix: 修复日期匹配算法显示错误"

# 文档更新
git commit -m "docs: 更新用户使用手册"

# 版本发布
git commit -m "release: v1.0.1 - 修复日期匹配Bug"
```

## 🏷️ 版本标签管理

### 语义化版本标签

```bash
# 主版本更新
git tag -a v2.0.0 -m "Major release: 全新架构设计"

# 次版本更新
git tag -a v1.1.0 -m "Minor release: 添加批量处理功能"

# 修订版本更新
git tag -a v1.0.1 -m "Patch release: 修复日期匹配Bug"
```

### 标签管理最佳实践

```bash
# 查看所有标签
git tag

# 查看特定标签的详细信息
git show v1.0.1

# 推送标签到远程仓库
git push origin v1.0.1
git push origin --tags  # 推送所有标签

# 基于标签创建发布包
git archive --format=zip --output=releases/v1.0.1.zip v1.0.1
```

## 🔄 实际使用场景

### 场景1：修复Bug

```bash
# 1. 创建热修复分支
git checkout -b hotfix/fix-date-matching

# 2. 修复代码
# 编辑相关文件...

# 3. 提交修复
git add .
git commit -m "fix: 修复日期匹配算法显示错误"

# 4. 切换到主分支并合并
git checkout main
git merge hotfix/fix-date-matching

# 5. 创建版本标签
git tag -a v1.0.1 -m "Patch release: 修复日期匹配Bug"

# 6. 删除临时分支
git branch -d hotfix/fix-date-matching
```

### 场景2：开发新功能

```bash
# 1. 创建功能分支
git checkout -b feature/batch-processing

# 2. 开发新功能
# 编辑相关文件...

# 3. 分阶段提交
git add script.js
git commit -m "feat: 添加批量处理核心逻辑"

git add styles.css
git commit -m "style: 添加批量处理界面样式"

git add index.html
git commit -m "feat: 添加批量处理用户界面"

# 4. 切换到主分支并合并
git checkout main
git merge feature/batch-processing

# 5. 创建版本标签
git tag -a v1.1.0 -m "Minor release: 添加批量处理功能"

# 6. 删除功能分支
git branch -d feature/batch-processing
```

### 场景3：版本回滚

```bash
# 查看版本历史
git log --oneline

# 回滚到特定版本
git checkout v1.0.0

# 如果需要永久回滚，创建新分支
git checkout -b rollback-to-v1.0.0

# 或者重置到特定版本（谨慎使用）
git reset --hard v1.0.0
```

## 📦 发布管理

### 自动化发布脚本

创建 `scripts/git-release.bat`:

```batch
@echo off
echo Git版本发布工具
echo.

set /p version="请输入版本号 (如: 1.0.1): "
set /p message="请输入发布说明: "

echo 正在创建版本 v%version%...

REM 添加所有变更
git add .

REM 提交变更
git commit -m "release: v%version% - %message%"

REM 创建标签
git tag -a v%version% -m "Release version %version%: %message%"

REM 创建发布包
git archive --format=zip --output=releases/v%version%.zip v%version%

echo 版本 v%version% 发布完成！
echo 发布包已创建: releases/v%version%.zip
pause
```

### 发布检查清单

```bash
# 发布前检查
- [ ] 所有变更已提交
- [ ] 功能测试完成
- [ ] 文档已更新
- [ ] 版本号符合规范

# 发布命令
git add .
git commit -m "release: v1.0.1 - 修复日期匹配Bug"
git tag -a v1.0.1 -m "Release version 1.0.1"

# 发布后验证
- [ ] 标签创建成功
- [ ] 发布包生成
- [ ] 功能验证通过
```

## 🔍 Git工具推荐

### 图形化工具

1. **Git GUI** (Git自带)
   - 基本的图形界面
   - 适合简单操作

2. **SourceTree** (免费)
   - 功能强大的Git客户端
   - 可视化分支管理

3. **GitKraken** (付费)
   - 现代化界面
   - 强大的合并工具

4. **VS Code Git扩展**
   - 集成在编辑器中
   - 便于开发时使用

### 命令行增强

```bash
# 安装Git别名，简化命令
git config --global alias.st status
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.lg "log --oneline --graph"
```

## 📞 迁移指南

### 从简单脚本迁移到Git

```bash
# 1. 备份当前版本管理文件
mkdir backup
copy scripts\*.bat backup\
copy version.json backup\

# 2. 初始化Git仓库
git init

# 3. 创建首次提交
git add .
git commit -m "feat: 初始版本 - 从简单脚本迁移到Git"

# 4. 创建当前版本标签
git tag -a v1.0.0 -m "Initial release"

# 5. 可选：保留原有脚本作为辅助工具
```

### 团队协作设置

```bash
# 添加远程仓库（如果有）
git remote add origin <仓库地址>

# 推送到远程仓库
git push -u origin main
git push origin --tags
```

---

## ✅ Git版本管理最佳实践

1. **频繁提交** - 小步快跑，每个功能点都提交
2. **清晰的提交信息** - 遵循提交信息规范
3. **合理使用分支** - 功能开发使用分支
4. **定期打标签** - 重要版本节点打标签
5. **保持仓库整洁** - 使用.gitignore排除无关文件

Git版本管理将为您的项目提供更专业、更可靠的版本控制能力！
