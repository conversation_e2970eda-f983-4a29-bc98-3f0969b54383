<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git状态检查工具 - 菜篮子智能比价工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .status-section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .git-info {
            background: #e8f4fd;
        }
        .version-info {
            background: #d4edda;
        }
        .commands-info {
            background: #fff3cd;
        }
        .help-info {
            background: #f8d7da;
        }
        .command-box {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn.success { background-color: #27ae60; }
        .btn.warning { background-color: #f39c12; }
        .btn.danger { background-color: #e74c3c; }
        .status-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .status-icon {
            font-size: 20px;
            margin-right: 10px;
            min-width: 30px;
        }
        .git-commands {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .command-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .command-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .workflow-diagram {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Git状态检查工具</h1>
        
        <div class="status-section git-info">
            <h3>📋 Git仓库状态</h3>
            <div id="gitStatus">
                <div class="status-item">
                    <span class="status-icon">🔍</span>
                    <span>检查Git仓库状态...</span>
                </div>
            </div>
        </div>
        
        <div class="status-section version-info">
            <h3>🏷️ 版本信息</h3>
            <div id="versionInfo">
                <div class="status-item">
                    <span class="status-icon">📦</span>
                    <span>加载版本信息...</span>
                </div>
            </div>
        </div>
        
        <div class="status-section commands-info">
            <h3>⚡ 快速操作</h3>
            <div style="text-align: center; margin: 20px 0;">
                <button class="btn success" onclick="openGitInit()">初始化Git仓库</button>
                <button class="btn" onclick="openGitCommit()">提交变更</button>
                <button class="btn warning" onclick="openGitRelease()">创建发布</button>
                <a href="../Git版本管理指南.md" class="btn" target="_blank">查看指南</a>
            </div>
        </div>
        
        <div class="workflow-diagram">
            <h4>🔄 Git工作流程</h4>
            <div style="font-family: monospace; line-height: 2;">
                开发 → 暂存 → 提交 → 标签 → 发布<br>
                ↓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;↓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;↓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;↓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;↓<br>
                编码&nbsp;&nbsp;&nbsp;git add&nbsp;&nbsp;git commit&nbsp;&nbsp;git tag&nbsp;&nbsp;archive
            </div>
        </div>
        
        <div class="git-commands">
            <div class="command-card">
                <div class="command-title">📝 日常提交</div>
                <div class="command-box">
git add .<br>
git commit -m "feat: 添加新功能"
                </div>
            </div>
            
            <div class="command-card">
                <div class="command-title">🏷️ 版本标签</div>
                <div class="command-box">
git tag -a v1.0.1 -m "Release v1.0.1"<br>
git tag  # 查看所有标签
                </div>
            </div>
            
            <div class="command-card">
                <div class="command-title">📊 查看状态</div>
                <div class="command-box">
git status<br>
git log --oneline<br>
git diff
                </div>
            </div>
            
            <div class="command-card">
                <div class="command-title">🔄 分支管理</div>
                <div class="command-box">
git checkout -b feature/new-feature<br>
git merge feature/new-feature
                </div>
            </div>
        </div>
        
        <div class="status-section help-info">
            <h3>📞 帮助和资源</h3>
            <div class="status-item">
                <span class="status-icon">📖</span>
                <span>详细使用指南: <a href="../Git版本管理指南.md" target="_blank">Git版本管理指南.md</a></span>
            </div>
            <div class="status-item">
                <span class="status-icon">🛠️</span>
                <span>自动化脚本: scripts/git-*.bat</span>
            </div>
            <div class="status-item">
                <span class="status-icon">🌐</span>
                <span>Git官方文档: <a href="https://git-scm.com/docs" target="_blank">https://git-scm.com/docs</a></span>
            </div>
        </div>
    </div>

    <script>
        // 检查Git仓库状态
        function checkGitStatus() {
            const gitStatusDiv = document.getElementById('gitStatus');
            
            // 检查是否存在.git目录（简单检测）
            fetch('../.git/HEAD')
                .then(response => {
                    if (response.ok) {
                        gitStatusDiv.innerHTML = `
                            <div class="status-item">
                                <span class="status-icon">✅</span>
                                <span>Git仓库已初始化</span>
                            </div>
                            <div class="status-item">
                                <span class="status-icon">📁</span>
                                <span>仓库位置: ${window.location.pathname.replace('/scripts/git-status.html', '')}</span>
                            </div>
                            <div class="status-item">
                                <span class="status-icon">💡</span>
                                <span>使用命令行查看详细状态: <code>git status</code></span>
                            </div>
                        `;
                    } else {
                        throw new Error('Not a git repository');
                    }
                })
                .catch(error => {
                    gitStatusDiv.innerHTML = `
                        <div class="status-item">
                            <span class="status-icon">❌</span>
                            <span>Git仓库未初始化</span>
                        </div>
                        <div class="status-item">
                            <span class="status-icon">🚀</span>
                            <span>点击"初始化Git仓库"按钮开始使用Git版本管理</span>
                        </div>
                    `;
                });
        }
        
        // 加载版本信息
        function loadVersionInfo() {
            const versionDiv = document.getElementById('versionInfo');
            
            fetch('../version.json')
                .then(response => response.json())
                .then(data => {
                    versionDiv.innerHTML = `
                        <div class="status-item">
                            <span class="status-icon">🏷️</span>
                            <span>当前版本: v${data.version}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-icon">📅</span>
                            <span>构建日期: ${data.buildDate}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-icon">🔢</span>
                            <span>构建编号: ${data.buildNumber}</span>
                        </div>
                        <div class="status-item">
                            <span class="status-icon">📋</span>
                            <span>发布类型: ${data.releaseType}</span>
                        </div>
                    `;
                })
                .catch(error => {
                    versionDiv.innerHTML = `
                        <div class="status-item">
                            <span class="status-icon">⚠️</span>
                            <span>无法加载版本信息</span>
                        </div>
                    `;
                });
        }
        
        // 打开Git初始化脚本
        function openGitInit() {
            if (confirm('这将初始化Git仓库。是否继续？')) {
                // 在Windows中，我们不能直接执行.bat文件，只能提示用户
                alert('请在命令行中运行: scripts\\git-init.bat\n或者双击 scripts/git-init.bat 文件');
            }
        }
        
        // 打开Git提交脚本
        function openGitCommit() {
            alert('请在命令行中运行: scripts\\git-commit.bat\n或者双击 scripts/git-commit.bat 文件');
        }
        
        // 打开Git发布脚本
        function openGitRelease() {
            if (confirm('这将创建新的版本发布。是否继续？')) {
                alert('请在命令行中运行: scripts\\git-release.bat\n或者双击 scripts/git-release.bat 文件');
            }
        }
        
        // 页面加载时执行检查
        window.addEventListener('load', function() {
            checkGitStatus();
            loadVersionInfo();
        });
    </script>
</body>
</html>
