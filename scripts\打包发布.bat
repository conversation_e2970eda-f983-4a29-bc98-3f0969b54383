@echo off
chcp 65001 >nul
echo ========================================
echo 菜篮子智能比价工具 - 打包发布工具
echo ========================================
echo.

REM 检查必要文件
if not exist "version.json" (
    echo 错误：找不到version.json文件
    pause
    exit /b 1
)

if not exist "index.html" (
    echo 错误：找不到index.html文件
    pause
    exit /b 1
)

REM 读取版本号
for /f "tokens=2 delims=:" %%a in ('findstr "version" version.json') do (
    set version_line=%%a
)
set version=%version_line:"=%
set version=%version:,=%
set version=%version: =%

echo 当前版本: %version%
echo.

REM 创建发布目录
set release_dir=releases\v%version%
if not exist "releases" mkdir releases
if exist "%release_dir%" (
    echo 警告：版本 %version% 已存在
    set /p overwrite="是否覆盖? (y/n): "
    if /i not "%overwrite%"=="y" (
        echo 取消发布
        pause
        exit /b 1
    )
    rmdir /s /q "%release_dir%"
)

mkdir "%release_dir%"
echo 创建发布目录: %release_dir%

REM 复制核心文件
echo 正在复制文件...
copy index.html "%release_dir%\" >nul
copy styles.css "%release_dir%\" >nul
copy script.js "%release_dir%\" >nul
copy version.json "%release_dir%\" >nul
copy CHANGELOG.md "%release_dir%\" >nul
copy README.md "%release_dir%\" >nul

REM 复制目录
if exist "libs" (
    xcopy libs "%release_dir%\libs\" /e /i /q >nul
    echo 已复制 libs 目录
)

if exist "scripts" (
    xcopy scripts "%release_dir%\scripts\" /e /i /q >nul
    echo 已复制 scripts 目录
)

REM 复制文档
if exist "*.txt" (
    copy *.txt "%release_dir%\" >nul
    echo 已复制文档文件
)

if exist "*.md" (
    copy *.md "%release_dir%\" >nul
    echo 已复制Markdown文档
)

REM 创建发布说明
echo 正在生成发布说明...
(
echo 菜篮子智能比价工具 v%version%
echo ================================
echo.
echo 发布日期: %date%
echo 版本号: %version%
echo.
echo 文件清单:
echo - index.html ^(主应用页面^)
echo - styles.css ^(样式文件^)
echo - script.js ^(主要逻辑^)
echo - libs/xlsx.min.js ^(SheetJS库^)
echo - 各种文档和工具文件
echo.
echo 安装说明:
echo 1. 确保SheetJS库已正确安装
echo 2. 运行 start-server.bat 启动服务
echo 3. 在浏览器中访问 http://localhost:8080
echo.
echo 更多信息请参考 README.md 和相关文档
) > "%release_dir%\发布说明.txt"

REM 生成文件校验和
echo 正在生成校验和...
dir "%release_dir%" /s /-c > "%release_dir%\文件清单.txt"

REM 创建压缩包（如果有7zip或WinRAR）
where 7z >nul 2>&1
if %errorlevel%==0 (
    echo 正在创建压缩包...
    7z a -tzip "releases\菜篮子智能比价工具_v%version%.zip" "%release_dir%\*" >nul
    echo 已创建压缩包: releases\菜篮子智能比价工具_v%version%.zip
) else (
    echo 注意：未找到7zip，请手动创建压缩包
)

echo.
echo ========================================
echo 发布包创建完成！
echo ========================================
echo.
echo 发布目录: %release_dir%
echo 版本号: %version%
echo 发布时间: %date% %time%
echo.
echo 发布检查清单:
echo [ ] 功能测试完成
echo [ ] 文档更新完成
echo [ ] 版本信息正确
echo [ ] 文件完整性检查
echo [ ] 部署测试完成
echo.
echo 下一步操作:
echo 1. 测试发布包功能
echo 2. 更新部署文档
echo 3. 通知相关人员
echo 4. 归档旧版本
echo.

pause
