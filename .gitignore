# 菜篮子智能比价工具 - Git忽略文件配置

# ===========================================
# 操作系统生成的文件
# ===========================================

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===========================================
# 编辑器和IDE文件
# ===========================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Notepad++
*.bak

# ===========================================
# 临时文件和缓存
# ===========================================

# 浏览器缓存
*.tmp
*.temp
*.cache

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# ===========================================
# 构建和发布文件
# ===========================================

# 构建输出
build/
dist/
out/

# 依赖目录
node_modules/
bower_components/

# 包文件
*.tgz
*.tar.gz

# ===========================================
# 项目特定文件
# ===========================================

# 测试数据文件（可选，根据需要调整）
test-data/
sample-files/
*.xlsx
*.xls
!example-template.xlsx  # 保留模板文件

# 用户上传的文件
uploads/
user-files/

# 临时处理文件
temp/
tmp/

# 备份文件
backup/
*.backup
version.json.backup

# 发布包（如果使用Git管理，可以忽略）
releases/*.zip
releases/*.tar.gz

# ===========================================
# 安全和配置文件
# ===========================================

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 密钥文件
*.key
*.pem
*.p12
*.pfx

# 配置文件（包含敏感信息的）
config/local.json
config/production.json

# ===========================================
# 版本管理相关
# ===========================================

# 旧版本管理脚本的输出（如果保留脚本的话）
scripts/output/
scripts/temp/

# Git相关
.git/
*.orig

# ===========================================
# 其他
# ===========================================

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz

# 媒体文件（如果项目中有大型媒体文件）
*.mp4
*.avi
*.mov
*.wmv
*.mp3
*.wav
*.flac

# 文档的临时文件
~$*.docx
~$*.xlsx
~$*.pptx

# ===========================================
# 保留的重要文件（确保不被忽略）
# ===========================================

# 确保这些重要文件不被忽略
!.gitignore
!README.md
!CHANGELOG.md
!version.json
!index.html
!styles.css
!script.js
!libs/xlsx.min.js

# 确保文档文件不被忽略
!docs/
!*.md

# 确保脚本文件不被忽略（如果要保留的话）
!scripts/*.bat
!scripts/*.html

# ===========================================
# 自定义规则
# ===========================================

# 在这里添加项目特定的忽略规则
# 例如：
# my-custom-folder/
# *.custom-extension
