<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署检查 - 菜篮子智能比价工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .check-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .check-item.success {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .check-item.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .check-item.warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        .status {
            font-weight: bold;
            margin-right: 10px;
            min-width: 60px;
        }
        .success .status { color: #28a745; }
        .error .status { color: #dc3545; }
        .warning .status { color: #ffc107; }
        .description {
            flex: 1;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn.success {
            background-color: #28a745;
        }
        .btn.success:hover {
            background-color: #1e7e34;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 部署状态检查</h1>
        
        <div id="results">
            <div class="check-item" id="check-files">
                <span class="status">检查中...</span>
                <span class="description">检查必要文件是否存在</span>
            </div>
            
            <div class="check-item" id="check-xlsx">
                <span class="status">检查中...</span>
                <span class="description">检查SheetJS库是否正确加载</span>
            </div>
            
            <div class="check-item" id="check-browser">
                <span class="status">检查中...</span>
                <span class="description">检查浏览器兼容性</span>
            </div>
            
            <div class="check-item" id="check-features">
                <span class="status">检查中...</span>
                <span class="description">检查必要功能支持</span>
            </div>
        </div>
        
        <div class="actions">
            <button class="btn" onclick="runChecks()">重新检查</button>
            <a href="index.html" class="btn success" id="launchBtn" style="display:none;">启动应用</a>
        </div>
    </div>

    <script src="libs/xlsx.min.js"></script>
    <script>
        function updateCheckItem(id, status, message) {
            const item = document.getElementById(id);
            const statusSpan = item.querySelector('.status');
            const descSpan = item.querySelector('.description');
            
            item.className = 'check-item ' + status;
            statusSpan.textContent = status === 'success' ? '✓ 通过' : 
                                   status === 'error' ? '✗ 失败' : 
                                   '⚠ 警告';
            descSpan.textContent = message;
        }

        function checkFiles() {
            // 检查CSS文件
            const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
            let cssLoaded = false;
            
            // 简单检查：如果页面样式正常，说明CSS加载成功
            const testElement = document.createElement('div');
            testElement.style.display = 'none';
            document.body.appendChild(testElement);
            const computedStyle = window.getComputedStyle(testElement);
            document.body.removeChild(testElement);
            
            if (computedStyle) {
                cssLoaded = true;
            }
            
            if (cssLoaded) {
                updateCheckItem('check-files', 'success', '所有必要文件已正确加载');
            } else {
                updateCheckItem('check-files', 'error', '样式文件加载失败，请检查文件路径');
            }
            
            return cssLoaded;
        }

        function checkXLSX() {
            try {
                if (typeof XLSX !== 'undefined' && XLSX.read && XLSX.utils) {
                    // 尝试创建一个简单的工作簿来测试功能
                    const testData = [['测试', '数据'], ['1', '2']];
                    const ws = XLSX.utils.aoa_to_sheet(testData);
                    const wb = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(wb, ws, 'Test');
                    
                    updateCheckItem('check-xlsx', 'success', 'SheetJS库已正确加载并可正常使用');
                    return true;
                } else {
                    throw new Error('XLSX对象不完整');
                }
            } catch (error) {
                updateCheckItem('check-xlsx', 'error', 
                    'SheetJS库加载失败：' + error.message + 
                    '。请下载完整的xlsx.full.min.js文件并替换libs/xlsx.min.js');
                return false;
            }
        }

        function checkBrowser() {
            const features = {
                fileAPI: typeof FileReader !== 'undefined',
                arrayBuffer: typeof ArrayBuffer !== 'undefined',
                uint8Array: typeof Uint8Array !== 'undefined',
                promise: typeof Promise !== 'undefined',
                fetch: typeof fetch !== 'undefined'
            };
            
            const missing = Object.keys(features).filter(key => !features[key]);
            
            if (missing.length === 0) {
                updateCheckItem('check-browser', 'success', 
                    '浏览器完全兼容，支持所有必要功能');
                return true;
            } else if (missing.length <= 2) {
                updateCheckItem('check-browser', 'warning', 
                    '浏览器基本兼容，但缺少部分功能：' + missing.join(', '));
                return true;
            } else {
                updateCheckItem('check-browser', 'error', 
                    '浏览器不兼容，缺少关键功能：' + missing.join(', ') + 
                    '。请使用Chrome、Firefox或Edge最新版本');
                return false;
            }
        }

        function checkFeatures() {
            try {
                // 检查本地存储
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                
                // 检查JSON支持
                JSON.parse('{"test": true}');
                
                // 检查正则表达式
                /test/.test('test');
                
                updateCheckItem('check-features', 'success', '所有核心功能正常');
                return true;
            } catch (error) {
                updateCheckItem('check-features', 'error', '核心功能检查失败：' + error.message);
                return false;
            }
        }

        function runChecks() {
            const launchBtn = document.getElementById('launchBtn');
            launchBtn.style.display = 'none';
            
            setTimeout(() => {
                const results = [
                    checkFiles(),
                    checkXLSX(),
                    checkBrowser(),
                    checkFeatures()
                ];
                
                if (results.every(r => r)) {
                    launchBtn.style.display = 'inline-block';
                }
            }, 500);
        }

        // 页面加载完成后自动运行检查
        window.addEventListener('load', runChecks);
    </script>
</body>
</html>
