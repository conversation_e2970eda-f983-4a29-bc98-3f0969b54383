# Git版本管理 - 快速使用指南

## 🚀 5分钟上手Git

### 第一次使用（一次性设置）

```bash
# 1. 初始化Git仓库
双击 scripts/git-init.bat

# 或者手动执行：
git init
git config user.name "您的姓名"
git config user.email "您的邮箱"
git add .
git commit -m "feat: initial commit"
git tag -a v1.0.0 -m "Initial release"
```

### 日常使用（3步骤）

```bash
# 1. 提交变更
双击 scripts/git-commit.bat

# 2. 创建发布（当需要发布新版本时）
双击 scripts/git-release.bat

# 3. 检查状态
双击 scripts/git-status.html
```

## 📋 常用场景

### 场景1：修复Bug

```bash
# 方法1：使用脚本（推荐）
双击 scripts/git-commit.bat
选择 "2. fix - Bug fix"
输入描述："修复日期匹配显示错误"

# 方法2：手动命令
git add .
git commit -m "fix: 修复日期匹配显示错误"
```

### 场景2：添加新功能

```bash
# 方法1：使用脚本（推荐）
双击 scripts/git-commit.bat
选择 "1. feat - New feature"
输入描述："添加批量处理功能"

# 方法2：手动命令
git add .
git commit -m "feat: 添加批量处理功能"
```

### 场景3：发布新版本

```bash
# 方法1：使用脚本（推荐）
双击 scripts/git-release.bat
选择版本类型（1=Bug修复, 2=新功能, 3=重大更新）
输入发布说明

# 方法2：手动命令
git add .
git commit -m "release: v1.0.1 - 修复日期匹配Bug"
git tag -a v1.0.1 -m "Release version 1.0.1"
```

## 🔧 Git vs 简单脚本对比

| 操作 | 简单脚本方式 | Git方式 | 优势 |
|------|-------------|---------|------|
| **版本更新** | 手动编辑version.json | `git commit` | 自动记录变更历史 |
| **查看历史** | 查看CHANGELOG.md | `git log` | 完整的提交历史 |
| **版本回滚** | 手动恢复备份 | `git checkout v1.0.0` | 一键回滚到任意版本 |
| **文件对比** | 无 | `git diff` | 清晰显示文件变更 |
| **分支开发** | 无 | `git branch` | 并行开发不同功能 |
| **标签管理** | 手动创建目录 | `git tag` | 自动化标签管理 |

## ⚡ 常用命令速查

### 基础命令
```bash
git status          # 查看仓库状态
git add .           # 添加所有变更到暂存区
git commit -m "..."  # 提交变更
git log --oneline   # 查看提交历史
git diff            # 查看文件变更
```

### 版本管理
```bash
git tag                    # 查看所有标签
git tag -a v1.0.1 -m "..." # 创建标签
git checkout v1.0.0        # 切换到特定版本
git checkout main          # 回到最新版本
```

### 分支管理
```bash
git branch                 # 查看分支
git checkout -b feature/xxx # 创建并切换到新分支
git merge feature/xxx      # 合并分支
git branch -d feature/xxx  # 删除分支
```

## 🎯 推荐工作流

### 日常开发工作流
```
1. 开发功能/修复Bug
   ↓
2. 运行 git-commit.bat
   ↓
3. 选择提交类型
   ↓
4. 输入描述信息
   ↓
5. 完成提交
```

### 版本发布工作流
```
1. 确认所有功能完成
   ↓
2. 运行 git-release.bat
   ↓
3. 选择版本类型
   ↓
4. 输入发布说明
   ↓
5. 自动创建标签和发布包
```

## 🔍 状态检查

### 使用Git状态工具
```bash
# 打开Web界面检查
双击 scripts/git-status.html

# 或使用命令行
git status
git log --oneline -10
git tag
```

### 检查项目
- ✅ Git仓库已初始化
- ✅ 用户信息已配置
- ✅ 有提交历史
- ✅ 版本标签存在

## 🚨 常见问题

### 问题1：Git未安装
```bash
错误：'git' 不是内部或外部命令
解决：下载安装Git - https://git-scm.com/
```

### 问题2：用户信息未配置
```bash
错误：Please tell me who you are
解决：运行 git-init.bat 或手动配置用户信息
```

### 问题3：没有变更可提交
```bash
信息：nothing to commit, working tree clean
说明：当前没有文件变更，无需提交
```

### 问题4：标签已存在
```bash
错误：tag 'v1.0.1' already exists
解决：使用不同的版本号或删除现有标签
```

## 📁 文件结构

```
项目根目录/
├── .git/                 # Git仓库数据
├── .gitignore            # Git忽略文件配置
├── scripts/
│   ├── git-init.bat      # Git初始化脚本
│   ├── git-commit.bat    # Git提交脚本
│   ├── git-release.bat   # Git发布脚本
│   └── git-status.html   # Git状态检查工具
├── releases/             # 发布包存储目录
└── Git版本管理指南.md    # 详细使用指南
```

## 💡 最佳实践

### 提交信息规范
- `feat:` 新功能
- `fix:` Bug修复
- `docs:` 文档更新
- `style:` 代码格式
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建工具

### 版本号规范
- `1.0.1` - Bug修复（PATCH）
- `1.1.0` - 新功能（MINOR）
- `2.0.0` - 重大更新（MAJOR）

### 分支命名
- `main` - 主分支
- `develop` - 开发分支
- `feature/功能名` - 功能分支
- `hotfix/bug名` - 热修复分支

## 🎉 开始使用

### 立即开始（3步骤）
```bash
1. 双击 scripts/git-init.bat     # 初始化Git仓库
2. 双击 scripts/git-status.html  # 检查状态
3. 双击 scripts/git-commit.bat   # 开始第一次提交
```

### 学习更多
- 📖 阅读 `Git版本管理指南.md` 了解详细用法
- 🌐 访问 [Git官方文档](https://git-scm.com/docs)
- 💬 加入Git社区获取帮助

---

**Git版本管理让您的项目更专业、更安全、更高效！**
