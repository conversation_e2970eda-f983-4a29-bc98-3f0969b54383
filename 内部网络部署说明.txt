菜篮子智能比价工具 - 内部网络部署包
=============================================

本部署包已针对内部网络环境进行优化，包含以下文件：

核心应用文件：
├── index.html              # 主应用页面
├── styles.css              # 样式文件
├── script.js               # 主要逻辑代码
└── libs/
    └── xlsx.min.js         # SheetJS库（需要替换）

部署辅助文件：
├── 检查部署.html           # 部署状态检查工具
├── 启动本地服务器.bat      # 快速启动脚本
├── 下载SheetJS库.bat       # 库文件下载工具
├── 部署指南.md             # 详细部署文档
└── 内部网络部署说明.txt    # 本文件

文档文件：
├── README.md               # 项目说明文档
└── 使用说明.md             # 详细使用指南

部署前必须完成的步骤：
==============================

1. 【重要】替换SheetJS库文件
   - 当前 libs/xlsx.min.js 只是占位文件
   - 需要下载完整版本：https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
   - 可以使用 "下载SheetJS库.bat" 自动下载（需要网络）
   - 或手动下载后替换文件内容

2. 验证部署环境
   - 打开 "检查部署.html" 进行环境检查
   - 确保所有检查项都通过

3. 启动服务
   - 使用 "启动本地服务器.bat" 快速启动
   - 或参考 "部署指南.md" 配置Web服务器

部署方式选择：
==============

方式一：Web服务器部署（推荐）
- 适用于多用户访问
- 性能稳定，功能完整
- 支持IIS、Apache、Nginx等

方式二：本地文件访问
- 适用于单用户使用
- 直接双击index.html打开
- 可能受浏览器安全限制

方式三：简易HTTP服务器
- 适用于临时使用
- 需要Python或Node.js环境
- 使用启动脚本自动配置

技术要求：
==========

浏览器支持：
- Chrome 60+（推荐）
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11（功能受限）

系统要求：
- Windows 7+
- macOS 10.12+
- Linux（任意发行版）

网络要求：
- 部署时需要网络下载SheetJS库
- 运行时无需网络连接
- 所有数据处理在本地完成

安全特性：
==========

- 纯前端应用，无服务器端处理
- 文件不会上传到外部服务器
- 所有数据处理在浏览器本地完成
- 适合处理敏感数据

故障排除：
==========

常见问题：
1. 页面无法正常显示
   → 检查所有文件是否完整
   → 确认浏览器兼容性

2. SheetJS库错误
   → 确认已正确替换xlsx.min.js
   → 使用检查部署.html验证

3. 文件上传失败
   → 检查文件格式（仅支持Excel）
   → 确认文件未损坏

4. 功能异常
   → 打开浏览器开发者工具（F12）
   → 查看Console错误信息

联系支持：
==========

如遇到技术问题：
1. 首先运行 "检查部署.html" 诊断
2. 查看浏览器控制台错误信息
3. 参考 "部署指南.md" 详细说明
4. 联系技术支持人员

部署完成检查清单：
==================

□ SheetJS库文件已正确替换
□ 检查部署.html 所有项目通过
□ 应用可以正常打开
□ 文件上传功能正常
□ 比价分析功能正常
□ 结果导出功能正常

完成以上检查后，应用即可正常使用。

最后更新：2024年12月
版本：1.0
