/*! xlsx.js (C) 2013-present SheetJ<PERSON> -- http://sheetjs.com */
/* This is a placeholder file. The actual SheetJS library should be downloaded from:
   https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
   
   For internal network deployment, please:
   1. Download the full library from the CDN link above
   2. Replace this file content with the downloaded content
   3. Update the script src in index.html to point to this local file
*/

// Placeholder to prevent errors
if (typeof XLSX === 'undefined') {
    window.XLSX = {
        read: function() { 
            throw new Error('SheetJS library not loaded. Please download the full library.'); 
        },
        utils: {
            sheet_to_json: function() { 
                throw new Error('SheetJS library not loaded. Please download the full library.'); 
            }
        }
    };
}
