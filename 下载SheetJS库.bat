@echo off
echo ========================================
echo SheetJS Library Downloader
echo ========================================
echo.

echo Checking network connection...
ping -n 1 cdnjs.cloudflare.com >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Cannot connect to CDN server
    echo Please manually download from:
    echo https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
    echo.
    echo After download, replace the content of libs\xlsx.min.js
    pause
    exit /b 1
)

echo [OK] Network connection is working

REM Check if curl is available
curl --version >nul 2>&1
if %errorlevel% == 0 (
    echo Using curl to download SheetJS library...
    curl -L "https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js" -o "libs\xlsx.min.js"
    if %errorlevel% == 0 (
        echo [OK] SheetJS library downloaded successfully!
        echo File saved to: libs\xlsx.min.js
        goto :verify
    ) else (
        echo [ERROR] Download failed
        goto :manual
    )
)

REM Check if PowerShell is available
powershell -Command "Get-Command Invoke-WebRequest" >nul 2>&1
if %errorlevel% == 0 (
    echo Using PowerShell to download SheetJS library...
    powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js' -OutFile 'libs\xlsx.min.js'"
    if %errorlevel% == 0 (
        echo [OK] SheetJS library downloaded successfully!
        echo File saved to: libs\xlsx.min.js
        goto :verify
    ) else (
        echo [ERROR] Download failed
        goto :manual
    )
)

:manual
echo [ERROR] Automatic download failed
echo.
echo Please manually download SheetJS library:
echo 1. Visit: https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
echo 2. Save the file content
echo 3. Replace the content of libs\xlsx.min.js file
echo.
pause
exit /b 1

:verify
echo.
echo Verifying file...
if exist "libs\xlsx.min.js" (
    for %%A in ("libs\xlsx.min.js") do (
        if %%~zA gtr 1000000 (
            echo [OK] File size is normal (%%~zA bytes)
            echo [OK] SheetJS library installation completed!
            echo.
            echo You can now run "检查部署.html" to verify the installation
        ) else (
            echo [ERROR] File size is abnormal, download may be incomplete
            echo Please re-download or manually replace the file
        )
    )
) else (
    echo [ERROR] File does not exist
)

echo.
pause
