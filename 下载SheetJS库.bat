@echo off
chcp 65001 >nul
echo ========================================
echo SheetJS库下载工具
echo ========================================
echo.

echo 正在检查网络连接...
ping -n 1 cdnjs.cloudflare.com >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 无法连接到CDN服务器
    echo 请在有网络的环境中手动下载：
    echo https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
    echo.
    echo 下载后请将内容替换 libs\xlsx.min.js 文件
    pause
    exit /b 1
)

echo ✅ 网络连接正常

REM 检查curl是否可用
curl --version >nul 2>&1
if %errorlevel% == 0 (
    echo 使用curl下载SheetJS库...
    curl -L "https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js" -o "libs\xlsx.min.js"
    if %errorlevel% == 0 (
        echo ✅ SheetJS库下载成功！
        echo 文件已保存到: libs\xlsx.min.js
        goto :verify
    ) else (
        echo ❌ 下载失败
        goto :manual
    )
)

REM 检查PowerShell是否可用
powershell -Command "Get-Command Invoke-WebRequest" >nul 2>&1
if %errorlevel% == 0 (
    echo 使用PowerShell下载SheetJS库...
    powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js' -OutFile 'libs\xlsx.min.js'"
    if %errorlevel% == 0 (
        echo ✅ SheetJS库下载成功！
        echo 文件已保存到: libs\xlsx.min.js
        goto :verify
    ) else (
        echo ❌ 下载失败
        goto :manual
    )
)

:manual
echo ❌ 自动下载失败
echo.
echo 请手动下载SheetJS库：
echo 1. 访问: https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
echo 2. 保存文件内容
echo 3. 替换 libs\xlsx.min.js 文件的内容
echo.
pause
exit /b 1

:verify
echo.
echo 正在验证文件...
if exist "libs\xlsx.min.js" (
    for %%A in ("libs\xlsx.min.js") do (
        if %%~zA gtr 1000000 (
            echo ✅ 文件大小正常 (%%~zA 字节)
            echo ✅ SheetJS库安装完成！
            echo.
            echo 现在可以运行 检查部署.html 来验证安装
        ) else (
            echo ❌ 文件大小异常，可能下载不完整
            echo 请重新下载或手动替换文件
        )
    )
) else (
    echo ❌ 文件不存在
)

echo.
pause
