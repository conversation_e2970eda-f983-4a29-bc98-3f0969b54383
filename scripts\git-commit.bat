@echo off
echo ========================================
echo Git Commit Tool
echo ========================================
echo.

REM Check if in Git repository
if not exist ".git" (
    echo [ERROR] Not a Git repository
    echo Please run git-init.bat first
    pause
    exit /b 1
)

REM Show current status
echo Current repository status:
git status --short
echo.

REM Check if there are changes to commit
git diff-index --quiet HEAD --
if %errorlevel% == 0 (
    echo [INFO] No changes to commit
    pause
    exit /b 0
)

echo Select commit type:
echo 1. feat     - New feature
echo 2. fix      - Bug fix
echo 3. docs     - Documentation update
echo 4. style    - Code style changes
echo 5. refactor - Code refactoring
echo 6. test     - Test related changes
echo 7. chore    - Build process or auxiliary tools
echo 8. release  - Version release
echo 9. custom   - Custom commit message
echo.

set /p choice="Enter your choice (1-9): "

REM Set commit type based on choice
if "%choice%"=="1" set commit_type=feat
if "%choice%"=="2" set commit_type=fix
if "%choice%"=="3" set commit_type=docs
if "%choice%"=="4" set commit_type=style
if "%choice%"=="5" set commit_type=refactor
if "%choice%"=="6" set commit_type=test
if "%choice%"=="7" set commit_type=chore
if "%choice%"=="8" set commit_type=release
if "%choice%"=="9" goto custom_message

if "%commit_type%"=="" (
    echo [ERROR] Invalid choice
    pause
    exit /b 1
)

REM Get commit description
set /p description="Enter commit description: "

if "%description%"=="" (
    echo [ERROR] Description cannot be empty
    pause
    exit /b 1
)

REM Construct commit message
set commit_message=%commit_type%: %description%
goto commit

:custom_message
set /p commit_message="Enter custom commit message: "
if "%commit_message%"=="" (
    echo [ERROR] Commit message cannot be empty
    pause
    exit /b 1
)

:commit
echo.
echo Commit message: %commit_message%
echo.

REM Show files to be committed
echo Files to be committed:
git diff --cached --name-only
git diff --name-only

echo.
set /p confirm="Proceed with commit? (y/n): "
if /i not "%confirm%"=="y" (
    echo Commit cancelled
    pause
    exit /b 0
)

REM Add all changes
git add .

REM Create commit
git commit -m "%commit_message%"

if %errorlevel% == 0 (
    echo [OK] Commit successful!
    echo.
    echo Latest commits:
    git log --oneline -5
) else (
    echo [ERROR] Commit failed
)

echo.
pause
