@echo off
echo ========================================
echo Git Commit Tool
echo ========================================
echo.

if not exist "../.git" (
    echo ERROR: Not a Git repository
    echo Please run git-init.bat first
    pause
    exit /b 1
)

cd ..

echo Current status:
git status --short
echo.

git diff-index --quiet HEAD --
if %errorlevel% == 0 (
    echo No changes to commit
    pause
    exit /b 0
)

echo Select commit type:
echo 1. feat - New feature
echo 2. fix - Bug fix
echo 3. docs - Documentation
echo 4. style - Code style
echo 5. refactor - Refactoring
echo 6. test - Testing
echo 7. chore - Build tools
echo 8. release - Version release
echo 9. custom - Custom message
echo.

set /p choice="Choice (1-9): "

if "%choice%"=="1" set commit_type=feat
if "%choice%"=="2" set commit_type=fix
if "%choice%"=="3" set commit_type=docs
if "%choice%"=="4" set commit_type=style
if "%choice%"=="5" set commit_type=refactor
if "%choice%"=="6" set commit_type=test
if "%choice%"=="7" set commit_type=chore
if "%choice%"=="8" set commit_type=release
if "%choice%"=="9" goto custom_message

if "%commit_type%"=="" (
    echo Invalid choice
    pause
    exit /b 1
)

set /p description="Description: "

if "%description%"=="" (
    echo Description required
    pause
    exit /b 1
)

set commit_message=%commit_type%: %description%
goto commit

:custom_message
set /p commit_message="Custom message: "
if "%commit_message%"=="" (
    echo Message required
    pause
    exit /b 1
)

:commit
echo.
echo Message: %commit_message%
echo.

echo Files to commit:
git diff --name-only
echo.

set /p confirm="Proceed? (y/n): "
if /i not "%confirm%"=="y" (
    echo Cancelled
    pause
    exit /b 0
)

git add .
git commit -m "%commit_message%"

if %errorlevel% == 0 (
    echo Commit successful!
    echo.
    echo Recent commits:
    git log --oneline -5
) else (
    echo Commit failed
)

echo.
pause
