菜篮子智能比价工具 - SheetJS库安装指南
==========================================

重要提示：工具需要SheetJS库才能正常工作！

当前状态：❌ 未安装
目标状态：✅ 已安装

安装步骤：
=========

1. 打开浏览器，访问以下网址：
   https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js

2. 在打开的页面中：
   - 按 Ctrl+A 全选所有内容
   - 按 Ctrl+C 复制所有内容

3. 打开本工具目录中的文件：
   libs/xlsx.min.js

4. 在文本编辑器中：
   - 按 Ctrl+A 全选文件中的所有内容
   - 按 Ctrl+V 粘贴刚才复制的内容
   - 按 Ctrl+S 保存文件

5. 验证安装：
   - 打开 检查部署.html
   - 查看SheetJS库检查项是否显示"✓ 通过"

注意事项：
=========

- 下载的文件大约2MB，请确保完整复制
- 如果网络访问受限，可以请有外网的同事帮助下载
- 安装完成后，工具即可在内网环境中正常使用
- 只需要安装一次，后续无需重复操作

故障排除：
=========

如果安装后仍然出现错误：
1. 检查文件大小是否约为2MB
2. 确认文件内容以 "/*! xlsx.js" 开头
3. 重新下载并替换文件内容
4. 清除浏览器缓存后重试

技术支持：
=========

如遇问题，请联系技术支持并提供：
- 错误截图
- 文件大小信息
- 浏览器版本信息
