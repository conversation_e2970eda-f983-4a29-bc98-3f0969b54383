@echo off
chcp 65001 >nul
echo ========================================
echo 菜篮子智能比价工具 - 版本更新工具
echo ========================================
echo.

REM 检查是否存在version.json文件
if not exist "version.json" (
    echo 错误：找不到version.json文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 当前版本信息：
type version.json | findstr "version"
echo.

echo 请选择更新类型：
echo 1. 修订版本 (PATCH) - Bug修复
echo 2. 次要版本 (MINOR) - 新功能
echo 3. 主要版本 (MAJOR) - 重大更新
echo 4. 自定义版本号
echo.

set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto patch
if "%choice%"=="2" goto minor
if "%choice%"=="3" goto major
if "%choice%"=="4" goto custom
goto invalid

:patch
echo 更新修订版本...
REM 这里可以添加自动版本号递增逻辑
goto update

:minor
echo 更新次要版本...
goto update

:major
echo 更新主要版本...
goto update

:custom
set /p newversion="请输入新版本号 (格式: x.y.z): "
echo 设置版本号为: %newversion%
goto update

:update
echo.
echo 正在更新版本信息...

REM 获取当前日期
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "buildDate=%YYYY%-%MM%-%DD%"
set "buildNumber=%YYYY%%MM%%DD%001"

echo 构建日期: %buildDate%
echo 构建编号: %buildNumber%

REM 备份当前版本文件
copy version.json version.json.backup >nul
echo 已备份当前版本文件

echo.
echo 版本更新完成！
echo 请手动编辑 version.json 文件以完成版本信息更新
echo 不要忘记更新 CHANGELOG.md 文件
echo.

echo 下一步操作：
echo 1. 编辑 version.json 更新版本号
echo 2. 更新 CHANGELOG.md 添加变更记录
echo 3. 测试新版本功能
echo 4. 运行 打包发布.bat 创建发布包
echo.

pause
goto end

:invalid
echo 无效选择，请重新运行脚本
pause
goto end

:end
