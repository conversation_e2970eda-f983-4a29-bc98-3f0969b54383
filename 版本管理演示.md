# 版本管理系统实际演示

## 🎯 演示场景：修复一个Bug并发布新版本

假设我们发现了一个日期匹配的Bug，需要修复并发布新版本。

### 步骤1：确认当前版本状态

**操作：查看当前版本**
```bash
# 方法1：查看主页面底部
打开 index.html → 查看页面底部版本信息

# 方法2：查看版本文件
打开 version.json
```

**当前状态：**
```json
{
  "version": "1.0.0",
  "buildDate": "2024-12-19",
  "releaseType": "stable"
}
```

### 步骤2：修复Bug

**问题描述：**
- 日期匹配算法在处理26日数据时出现错误
- 总是显示25日而不是实际选中的26日

**修复代码：**
```javascript
// 在 script.js 中修复日期匹配逻辑
// (实际的代码修复已经完成)
```

**测试修复：**
```bash
# 1. 打开应用测试功能
# 2. 上传包含26日数据的Excel文件
# 3. 确认显示正确的日期
# 4. 验证其他功能正常
```

### 步骤3：更新版本信息

**运行版本更新脚本：**
```bash
# 双击运行
scripts/版本更新.bat
```

**脚本交互过程：**
```
========================================
菜篮子智能比价工具 - 版本更新工具
========================================

当前版本信息：
"version": "1.0.0",

请选择更新类型：
1. 修订版本 (PATCH) - Bug修复     ← 选择这个
2. 次要版本 (MINOR) - 新功能
3. 主要版本 (MAJOR) - 重大更新
4. 自定义版本号

请输入选择 (1-4): 1

更新修订版本...
正在更新版本信息...
构建日期: 2024-12-20
构建编号: 20241220001
已备份当前版本文件

版本更新完成！
请手动编辑 version.json 文件以完成版本信息更新
```

### 步骤4：编辑版本配置文件

**编辑 version.json：**
```json
{
  "version": "1.0.1",                    // 更新版本号
  "buildDate": "2024-12-20",             // 更新构建日期
  "buildNumber": "20241220001",          // 更新构建编号
  "releaseType": "stable",
  "description": "菜篮子智能比价工具 - 修复日期匹配Bug",
  "features": [
    "Excel文件解析和比价分析",
    "智能日期匹配算法",
    "可视化比价结果展示", 
    "数据导出功能",
    "内部网络部署支持"
  ],
  "requirements": {
    "browser": "Chrome 60+, Firefox 55+, Safari 12+, Edge 79+",
    "javascript": "ES6+",
    "dependencies": {
      "sheetjs": "0.18.5"
    }
  },
  "changelog": {
    "1.0.1": {                           // 添加新版本记录
      "date": "2024-12-20",
      "type": "patch",
      "changes": [
        "修复了日期匹配算法的Bug",
        "确保显示实际选中的日期而不是固定日期"
      ],
      "fixes": [
        "修复日期显示总是显示25日的问题",
        "解决日期匹配优先级算法的边界情况"
      ],
      "breaking": []
    },
    "1.0.0": {
      "date": "2024-12-19", 
      "type": "major",
      "changes": [
        "首次发布内部网络版本",
        "实现Excel文件比价分析功能"
      ]
    }
  }
}
```

**编辑 CHANGELOG.md：**
```markdown
# 变更日志

## [未发布]

### 计划新增
- 支持更多Excel格式
- 添加数据导入模板

## [1.0.1] - 2024-12-20

### 修复
- 修复了日期匹配算法的Bug，确保显示实际选中的日期
- 解决了日期显示总是显示25日而不是实际选中日期的问题
- 修复了日期匹配优先级算法在边界情况下的错误

### 修改
- 优化了日期匹配逻辑的性能
- 改进了错误处理机制

## [1.0.0] - 2024-12-19

### 新增
- 🎉 首次发布菜篮子智能比价工具
- 📊 Excel文件解析和比价分析功能
- 🧠 智能日期匹配算法
```

### 步骤5：验证版本更新

**运行版本检查工具：**
```bash
# 双击运行
scripts/版本检查.html
```

**检查结果：**
```
🔍 版本检查工具

📋 当前版本信息
版本号：1.0.1
构建日期：2024-12-20
构建编号：20241220001
发布类型：stable
描述：菜篮子智能比价工具 - 修复日期匹配Bug

✓ 通过  版本文件加载成功
✓ 通过  所有核心文件完整
✓ 通过  SheetJS库已正确加载
✓ 通过  浏览器完全兼容
```

**测试应用功能：**
```bash
# 1. 打开 index.html
# 2. 查看底部版本信息：v1.0.1 (2024-12-20)
# 3. 测试日期匹配功能
# 4. 确认Bug已修复
```

### 步骤6：打包发布

**运行打包脚本：**
```bash
# 双击运行
scripts/打包发布.bat
```

**打包过程：**
```
========================================
菜篮子智能比价工具 - 打包发布工具
========================================

当前版本: 1.0.1

创建发布目录: releases\v1.0.1
正在复制文件...
已复制 libs 目录
已复制 scripts 目录
已复制文档文件
已复制Markdown文档

正在生成发布说明...
正在生成校验和...
正在创建压缩包...
已创建压缩包: releases\菜篮子智能比价工具_v1.0.1.zip

========================================
发布包创建完成！
========================================

发布目录: releases\v1.0.1
版本号: 1.0.1
发布时间: 2024-12-20 15:30:45

发布检查清单:
[✓] 功能测试完成
[✓] 文档更新完成
[✓] 版本信息正确
[✓] 文件完整性检查
[✓] 部署测试完成
```

### 步骤7：验证发布包

**检查发布目录：**
```
releases/v1.0.1/
├── index.html
├── styles.css
├── script.js
├── version.json
├── CHANGELOG.md
├── README.md
├── libs/
│   └── xlsx.min.js
├── scripts/
│   ├── 版本更新.bat
│   ├── 打包发布.bat
│   └── 版本检查.html
├── 发布说明.txt
└── 文件清单.txt
```

**测试发布包：**
```bash
# 1. 进入 releases/v1.0.1/ 目录
# 2. 打开 index.html
# 3. 验证所有功能正常
# 4. 确认版本信息正确
```

### 步骤8：部署和通知

**部署到生产环境：**
```bash
# 1. 备份当前生产版本
# 2. 将发布包内容复制到生产环境
# 3. 验证部署成功
# 4. 更新相关文档
```

**通知相关人员：**
```
主题：菜篮子智能比价工具 v1.0.1 发布通知

内容：
各位同事，

菜篮子智能比价工具已更新到 v1.0.1 版本。

主要更新：
- 修复了日期匹配算法的Bug
- 确保显示实际选中的日期

更新时间：2024-12-20
版本类型：Bug修复版本

详细变更请查看 CHANGELOG.md 文件。

如有问题请联系技术支持。
```

## 📊 演示总结

### 整个流程用时
- **准备和测试**：20分钟
- **版本更新**：5分钟
- **文件编辑**：10分钟
- **验证检查**：5分钟
- **打包发布**：5分钟
- **部署通知**：10分钟
- **总计**：55分钟

### 关键要点
1. **严格按流程执行**：确保不遗漏任何步骤
2. **充分测试验证**：每个环节都要验证
3. **详细记录变更**：便于后续追溯
4. **及时通知相关人员**：确保信息同步

### 下次版本更新
- 如果是新功能：选择次版本更新 (1.0.1 → 1.1.0)
- 如果是重大更新：选择主版本更新 (1.0.1 → 2.0.0)
- 如果是小修复：继续修订版本 (1.0.1 → 1.0.2)

这个演示展示了完整的版本管理流程，您可以按照这个模式进行后续的版本管理工作！
