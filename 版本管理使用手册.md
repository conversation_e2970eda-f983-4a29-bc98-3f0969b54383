# 菜篮子智能比价工具 - 版本管理使用手册

## 🚀 快速开始

### 第一次使用版本管理系统

1. **检查当前状态**
   ```bash
   # 打开版本检查工具
   双击 scripts/版本检查.html
   ```

2. **查看当前版本信息**
   - 打开主应用 `index.html`
   - 查看页面底部的版本信息
   - 点击"版本检查"链接进行详细检查

3. **了解版本历史**
   ```bash
   # 查看变更日志
   打开 CHANGELOG.md
   ```

## 📋 日常使用场景

### 场景1：修复Bug (修订版本更新)

**步骤1：开发和测试**
```bash
# 1. 修复代码中的Bug
# 2. 测试修复效果
# 3. 确认功能正常
```

**步骤2：更新版本**
```bash
# 运行版本更新脚本
双击 scripts/版本更新.bat

# 选择 "1. 修订版本 (PATCH) - Bug修复"
```

**步骤3：编辑版本信息**
```json
// 编辑 version.json
{
  "version": "1.0.1",  // 从1.0.0更新到1.0.1
  "buildDate": "2024-12-20",
  "description": "修复了文件上传Bug"
}
```

**步骤4：更新变更日志**
```markdown
// 在 CHANGELOG.md 中添加
## [1.0.1] - 2024-12-20

### 修复
- 修复了Excel文件上传失败的问题
- 解决了日期匹配算法的边界情况Bug
```

**步骤5：打包发布**
```bash
# 运行打包脚本
双击 scripts/打包发布.bat
```

### 场景2：添加新功能 (次版本更新)

**步骤1：开发新功能**
```bash
# 1. 开发新的数据导出功能
# 2. 添加新的用户界面
# 3. 完整测试新功能
```

**步骤2：更新版本**
```bash
# 运行版本更新脚本
双击 scripts/版本更新.bat

# 选择 "2. 次要版本 (MINOR) - 新功能"
```

**步骤3：编辑版本信息**
```json
// 编辑 version.json
{
  "version": "1.1.0",  // 从1.0.1更新到1.1.0
  "buildDate": "2024-12-25",
  "features": [
    "Excel文件解析和比价分析",
    "智能日期匹配算法", 
    "可视化比价结果展示",
    "数据导出功能",
    "新增：批量数据处理功能",  // 新增功能
    "新增：高级筛选选项"        // 新增功能
  ]
}
```

**步骤4：更新变更日志**
```markdown
// 在 CHANGELOG.md 中添加
## [1.1.0] - 2024-12-25

### 新增
- 添加批量数据处理功能
- 新增高级筛选选项
- 支持自定义导出格式

### 修改
- 优化了用户界面布局
- 改进了数据处理性能
```

### 场景3：重大更新 (主版本更新)

**步骤1：重大架构变更**
```bash
# 1. 重构核心算法
# 2. 更新用户界面
# 3. 可能的不兼容变更
```

**步骤2：更新版本**
```bash
# 运行版本更新脚本
双击 scripts/版本更新.bat

# 选择 "3. 主要版本 (MAJOR) - 重大更新"
```

**步骤3：编辑版本信息**
```json
// 编辑 version.json
{
  "version": "2.0.0",  // 从1.x.x更新到2.0.0
  "buildDate": "2025-01-01",
  "description": "菜篮子智能比价工具 2.0 - 全新架构版本"
}
```

## 🔧 工具使用详解

### 1. 版本更新脚本使用

```bash
# 运行脚本
双击 scripts/版本更新.bat

# 脚本会显示：
========================================
菜篮子智能比价工具 - 版本更新工具
========================================

当前版本信息：
"version": "1.0.0",

请选择更新类型：
1. 修订版本 (PATCH) - Bug修复
2. 次要版本 (MINOR) - 新功能  
3. 主要版本 (MAJOR) - 重大更新
4. 自定义版本号

请输入选择 (1-4): 
```

**选择说明：**
- **选项1**: Bug修复，版本号第三位+1 (1.0.0 → 1.0.1)
- **选项2**: 新功能，版本号第二位+1 (1.0.0 → 1.1.0)
- **选项3**: 重大更新，版本号第一位+1 (1.0.0 → 2.0.0)
- **选项4**: 自定义版本号，手动输入

### 2. 打包发布脚本使用

```bash
# 运行脚本
双击 scripts/打包发布.bat

# 脚本会自动：
1. 读取当前版本号
2. 创建发布目录 releases/v1.0.1/
3. 复制所有必要文件
4. 生成发布说明
5. 创建压缩包（如果有7zip）
6. 显示发布检查清单
```

**输出示例：**
```
========================================
发布包创建完成！
========================================

发布目录: releases\v1.0.1
版本号: 1.0.1
发布时间: 2024-12-20 14:30:25

发布检查清单:
[ ] 功能测试完成
[ ] 文档更新完成  
[ ] 版本信息正确
[ ] 文件完整性检查
[ ] 部署测试完成
```

### 3. 版本检查工具使用

```bash
# 打开检查工具
双击 scripts/版本检查.html

# 工具会自动检查：
✓ 版本文件是否存在
✓ 核心文件完整性
✓ 依赖库版本
✓ 浏览器兼容性
```

## 📝 文件编辑指南

### version.json 编辑要点

```json
{
  "version": "1.0.1",           // 必须更新
  "buildDate": "2024-12-20",    // 必须更新为当前日期
  "buildNumber": "20241220001", // 自动生成或手动更新
  "releaseType": "stable",      // stable/beta/alpha
  "description": "...",         // 更新描述
  "features": [...],            // 更新功能列表
  "changelog": {                // 添加新版本记录
    "1.0.1": {
      "date": "2024-12-20",
      "type": "patch",
      "changes": ["修复了..."],
      "fixes": ["解决了..."]
    }
  }
}
```

### CHANGELOG.md 编辑格式

```markdown
# 变更日志

## [未发布]
### 计划新增
- 即将添加的功能

## [1.0.1] - 2024-12-20
### 修复
- 修复了文件上传Bug
- 解决了日期匹配问题

### 修改  
- 优化了性能
- 改进了用户界面

## [1.0.0] - 2024-12-19
### 新增
- 首次发布
```

## 🔄 版本发布流程

### 完整发布流程图

```
开发完成 → 版本更新 → 编辑信息 → 测试验证 → 打包发布 → 部署上线
    ↓         ↓         ↓         ↓         ↓         ↓
  代码提交   运行脚本   编辑文件   功能测试   创建包    更新环境
```

### 详细步骤清单

**准备阶段 (5分钟)**
- [ ] 确认所有代码已提交
- [ ] 功能测试完成
- [ ] 文档更新完成

**版本更新 (10分钟)**
- [ ] 运行 `scripts/版本更新.bat`
- [ ] 选择合适的版本类型
- [ ] 编辑 `version.json`
- [ ] 更新 `CHANGELOG.md`

**验证测试 (15分钟)**
- [ ] 运行 `scripts/版本检查.html`
- [ ] 确认所有检查项通过
- [ ] 测试核心功能
- [ ] 验证版本信息显示

**打包发布 (5分钟)**
- [ ] 运行 `scripts/打包发布.bat`
- [ ] 检查发布包完整性
- [ ] 验证压缩包内容
- [ ] 确认发布说明正确

**部署上线 (10分钟)**
- [ ] 备份当前版本
- [ ] 部署新版本
- [ ] 验证部署成功
- [ ] 通知相关人员

## 🚨 常见问题解决

### 问题1：版本脚本运行失败

**现象**：双击脚本无反应或报错
**解决**：
```bash
# 方法1：使用命令行运行
cmd /c scripts/版本更新.bat

# 方法2：检查文件权限
# 右键脚本 → 属性 → 确认可执行

# 方法3：手动编辑版本文件
# 直接编辑 version.json 和 CHANGELOG.md
```

### 问题2：版本信息不显示

**现象**：页面底部版本信息显示"加载中"
**解决**：
```bash
# 检查 version.json 文件格式
# 确认JSON语法正确
# 检查文件路径是否正确
```

### 问题3：打包失败

**现象**：打包脚本报错或文件缺失
**解决**：
```bash
# 检查必要文件是否存在
# 确认有足够磁盘空间
# 手动创建 releases 目录
```

## 📞 获取帮助

### 自助解决
1. 查看 `版本管理指南.md` 详细文档
2. 运行 `scripts/版本检查.html` 诊断问题
3. 检查 `CHANGELOG.md` 了解历史变更

### 技术支持
- 📧 邮箱：<EMAIL>
- 📱 电话：400-xxx-xxxx
- 💬 内部工单系统

---

## ✅ 使用检查清单

**每次版本更新时，请确认：**
- [ ] 已阅读本使用手册
- [ ] 选择了正确的版本类型
- [ ] 更新了所有必要文件
- [ ] 完成了测试验证
- [ ] 创建了发布包
- [ ] 通知了相关人员

**记住：版本管理是团队协作的重要工具，请严格按照流程执行！**
