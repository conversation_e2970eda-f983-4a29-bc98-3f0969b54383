@echo off
echo ========================================
echo Git Release Tool
echo ========================================
echo.

REM Check if in Git repository
if not exist ".git" (
    echo [ERROR] Not a Git repository
    echo Please run git-init.bat first
    pause
    exit /b 1
)

REM Show current version
echo Current version tags:
git tag | sort /r
echo.

REM Get current version from version.json if exists
if exist "version.json" (
    echo Current version.json:
    findstr "version" version.json
    echo.
)

echo Select release type:
echo 1. Patch (x.y.Z) - Bug fixes
echo 2. Minor (x.Y.z) - New features
echo 3. Major (X.y.z) - Breaking changes
echo 4. Custom version
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" set release_type=patch
if "%choice%"=="2" set release_type=minor
if "%choice%"=="3" set release_type=major
if "%choice%"=="4" goto custom_version

if "%release_type%"=="" (
    echo [ERROR] Invalid choice
    pause
    exit /b 1
)

REM Get latest tag
for /f "tokens=*" %%i in ('git describe --tags --abbrev=0 2^>nul') do set latest_tag=%%i

if "%latest_tag%"=="" (
    echo [WARNING] No previous tags found, starting with v1.0.0
    set new_version=1.0.0
    goto get_description
)

echo Latest tag: %latest_tag%

REM Extract version numbers (assuming format vX.Y.Z)
set version_only=%latest_tag:v=%
for /f "tokens=1,2,3 delims=." %%a in ("%version_only%") do (
    set major=%%a
    set minor=%%b
    set patch=%%c
)

REM Calculate new version
if "%release_type%"=="patch" (
    set /a patch+=1
    set new_version=%major%.%minor%.%patch%
)
if "%release_type%"=="minor" (
    set /a minor+=1
    set new_version=%major%.%minor%.0
)
if "%release_type%"=="major" (
    set /a major+=1
    set new_version=%major%.0.0
)

goto get_description

:custom_version
set /p new_version="Enter version number (e.g., 1.0.1): "
if "%new_version%"=="" (
    echo [ERROR] Version cannot be empty
    pause
    exit /b 1
)

:get_description
echo.
echo New version will be: v%new_version%
echo.

set /p description="Enter release description: "
if "%description%"=="" (
    set description=Release version %new_version%
)

echo.
echo Release Summary:
echo - Version: v%new_version%
echo - Description: %description%
echo.

REM Show uncommitted changes
git status --porcelain | findstr /r "^.M\|^.A\|^.D" >nul
if %errorlevel% == 0 (
    echo [WARNING] You have uncommitted changes:
    git status --short
    echo.
    set /p commit_first="Commit changes first? (y/n): "
    if /i "%commit_first%"=="y" (
        call git-commit.bat
    )
)

set /p confirm="Proceed with release? (y/n): "
if /i not "%confirm%"=="y" (
    echo Release cancelled
    pause
    exit /b 0
)

echo.
echo Creating release...

REM Update version.json if it exists
if exist "version.json" (
    echo Updating version.json...
    REM This is a simple replacement - in practice you might want a more sophisticated approach
    powershell -Command "(Get-Content version.json) -replace '\"version\": \"[^\"]*\"', '\"version\": \"%new_version%\"' | Set-Content version.json"
    
    REM Update build date
    for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
    set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
    set "build_date=%YYYY%-%MM%-%DD%"
    
    powershell -Command "(Get-Content version.json) -replace '\"buildDate\": \"[^\"]*\"', '\"buildDate\": \"%build_date%\"' | Set-Content version.json"
    
    echo [OK] version.json updated
)

REM Add version.json changes if updated
if exist "version.json" (
    git add version.json
)

REM Create release commit
git commit -m "release: v%new_version% - %description%"

if %errorlevel% neq 0 (
    echo [WARNING] No changes to commit for release
)

REM Create tag
git tag -a v%new_version% -m "Release version %new_version%: %description%"

if %errorlevel% == 0 (
    echo [OK] Tag v%new_version% created successfully
) else (
    echo [ERROR] Failed to create tag
    pause
    exit /b 1
)

REM Create release archive
if not exist "releases" mkdir releases

echo Creating release archive...
git archive --format=zip --output=releases/v%new_version%.zip v%new_version%

if %errorlevel% == 0 (
    echo [OK] Release archive created: releases/v%new_version%.zip
) else (
    echo [WARNING] Failed to create release archive
)

REM Generate release notes
echo Creating release notes...
(
echo Release Notes for v%new_version%
echo ================================
echo.
echo Release Date: %build_date%
echo Version: v%new_version%
echo Description: %description%
echo.
echo Changes since last release:
git log --oneline %latest_tag%..HEAD 2>nul
echo.
echo Files in this release:
git ls-tree -r --name-only v%new_version%
) > releases/v%new_version%-release-notes.txt

echo [OK] Release notes created: releases/v%new_version%-release-notes.txt

echo.
echo ========================================
echo Release v%new_version% Complete!
echo ========================================
echo.
echo Summary:
echo - Tag: v%new_version%
echo - Archive: releases/v%new_version%.zip
echo - Release notes: releases/v%new_version%-release-notes.txt
echo.
echo Repository status:
git log --oneline -3
echo.
echo All tags:
git tag | sort /r
echo.

pause
