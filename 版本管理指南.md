# 菜篮子智能比价工具 - 版本管理指南

## 📋 版本管理概述

本指南提供了完整的版本管理策略，帮助您有效地管理软件的版本、更新和发布。

## 🏷️ 版本号规则

### 语义化版本控制 (Semantic Versioning)

采用 `主版本号.次版本号.修订号` 格式：

```
1.0.0
│ │ │
│ │ └── 修订号 (PATCH) - 向后兼容的问题修正
│ └──── 次版本号 (MINOR) - 向后兼容的功能性新增
└────── 主版本号 (MAJOR) - 不兼容的API修改
```

### 版本类型说明

- **主版本号 (MAJOR)**：重大功能变更、架构调整、不兼容更新
- **次版本号 (MINOR)**：新功能添加、功能增强、保持向后兼容
- **修订号 (PATCH)**：Bug修复、小幅优化、安全补丁

### 预发布版本

- `1.0.0-alpha.1` - 内部测试版
- `1.0.0-beta.1` - 公开测试版
- `1.0.0-rc.1` - 发布候选版

## 📁 版本管理文件结构

```
菜篮子智能比价工具/
├── version.json              # 版本信息文件
├── CHANGELOG.md              # 变更日志
├── 版本管理指南.md           # 本文件
├── scripts/
│   ├── 版本更新.bat          # 版本更新脚本
│   ├── 打包发布.bat          # 打包发布脚本
│   └── 版本检查.html         # 版本检查工具
├── releases/                 # 发布版本存档
│   ├── v1.0.0/
│   ├── v1.0.1/
│   └── ...
└── docs/
    ├── 发布说明/
    └── 升级指南/
```

## 🔄 版本更新流程

### 1. 开发阶段
```bash
# 开发新功能或修复Bug
# 更新代码
# 测试功能
```

### 2. 版本准备
```bash
# 更新 version.json
# 更新 CHANGELOG.md
# 更新文档
# 运行测试
```

### 3. 版本发布
```bash
# 运行 scripts/版本更新.bat
# 运行 scripts/打包发布.bat
# 创建发布包
# 部署到目标环境
```

### 4. 版本归档
```bash
# 将发布版本存档到 releases/ 目录
# 更新版本文档
# 通知相关人员
```

## 📝 变更日志管理

### CHANGELOG.md 格式

```markdown
# 变更日志

## [1.0.1] - 2024-12-20

### 新增
- 添加了新的数据验证功能
- 支持更多Excel格式

### 修改
- 优化了日期匹配算法
- 改进了用户界面

### 修复
- 修复了文件上传的Bug
- 解决了浏览器兼容性问题

### 移除
- 移除了过时的功能模块
```

### 变更类型

- **新增 (Added)**：新功能
- **修改 (Changed)**：现有功能的变更
- **弃用 (Deprecated)**：即将移除的功能
- **移除 (Removed)**：已移除的功能
- **修复 (Fixed)**：Bug修复
- **安全 (Security)**：安全相关的修复

## 🛠️ 版本管理工具

### 1. 版本信息文件 (version.json)

包含当前版本的完整信息：
- 版本号
- 构建日期
- 功能列表
- 依赖关系
- 变更历史

### 2. 版本更新脚本

自动化版本更新过程：
- 更新版本号
- 生成构建号
- 更新时间戳
- 验证版本信息

### 3. 打包发布脚本

自动化打包和发布：
- 创建发布包
- 生成校验和
- 创建发布说明
- 归档历史版本

## 📦 发布管理

### 发布类型

1. **稳定版 (Stable)**
   - 经过充分测试
   - 适合生产环境
   - 版本号：x.y.z

2. **测试版 (Beta)**
   - 功能基本完整
   - 需要用户测试
   - 版本号：x.y.z-beta.n

3. **开发版 (Alpha)**
   - 开发中版本
   - 内部测试使用
   - 版本号：x.y.z-alpha.n

### 发布检查清单

- [ ] 代码审查完成
- [ ] 功能测试通过
- [ ] 兼容性测试通过
- [ ] 文档更新完成
- [ ] 版本信息更新
- [ ] 变更日志更新
- [ ] 发布说明准备
- [ ] 备份当前版本

## 🔍 版本检查和验证

### 版本一致性检查

1. **文件版本检查**
   - 检查 version.json 中的版本号
   - 验证文件时间戳
   - 确认功能完整性

2. **依赖版本检查**
   - 验证 SheetJS 库版本
   - 检查浏览器兼容性
   - 确认环境要求

3. **功能版本检查**
   - 测试核心功能
   - 验证新增功能
   - 确认修复效果

## 📋 版本管理最佳实践

### 1. 版本规划

- **定期发布**：建议每月发布一次小版本
- **功能冻结**：发布前一周停止新功能开发
- **测试周期**：每个版本至少测试3天

### 2. 文档管理

- **同步更新**：代码和文档同步更新
- **版本标记**：文档中标明适用版本
- **向后兼容**：保持文档的向后兼容性

### 3. 备份策略

- **版本备份**：每个发布版本都要备份
- **配置备份**：备份配置文件和设置
- **数据备份**：备份用户数据和设置

### 4. 回滚计划

- **快速回滚**：准备快速回滚到上一版本的方案
- **数据恢复**：确保数据可以恢复到之前状态
- **通知机制**：建立版本问题的通知机制

## 🚀 升级指南

### 小版本升级 (1.0.0 → 1.0.1)

1. 备份当前版本
2. 下载新版本文件
3. 替换相关文件
4. 验证功能正常
5. 更新文档

### 大版本升级 (1.x.x → 2.0.0)

1. 阅读升级说明
2. 备份所有数据
3. 检查兼容性要求
4. 执行升级步骤
5. 迁移配置和数据
6. 全面功能测试

## 📞 技术支持

### 版本问题报告

如遇版本相关问题，请提供：
- 当前版本号
- 问题描述
- 错误截图
- 操作步骤
- 环境信息

### 联系方式

- 📧 邮箱：<EMAIL>
- 📱 电话：400-xxx-xxxx
- 💬 内部工单系统

---

**版本管理是软件质量保证的重要环节，请严格按照本指南执行！**
