@echo off
echo ========================================
echo Local Server Launcher
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python detected, starting HTTP server...
    echo Server address: http://localhost:8080
    echo Press Ctrl+C to stop server
    echo.
    python -m http.server 8080
    goto :end
)

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Node.js detected, checking http-server...
    npx http-server --version >nul 2>&1
    if %errorlevel% == 0 (
        echo Starting HTTP server...
        echo Server address: http://localhost:8080
        echo Press Ctrl+C to stop server
        echo.
        npx http-server -p 8080
        goto :end
    ) else (
        echo Installing http-server...
        npm install -g http-server
        if %errorlevel% == 0 (
            echo Starting HTTP server...
            echo Server address: http://localhost:8080
            echo Press Ctrl+C to stop server
            echo.
            http-server -p 8080
            goto :end
        )
    )
)

REM If no Python or Node.js, provide other options
echo No Python or Node.js environment detected
echo.
echo Please choose one of the following options:
echo 1. Open index.html directly in browser
echo 2. Install Python or Node.js and run this script again
echo 3. Configure IIS or other web server
echo.
echo Press any key to open index.html directly...
pause >nul
start index.html

:end
echo.
echo Server stopped
pause
