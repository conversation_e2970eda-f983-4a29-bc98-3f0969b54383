@echo off
chcp 65001 >nul
echo ========================================
echo 菜篮子智能比价工具 - 本地服务器启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 检测到Python环境，启动HTTP服务器...
    echo 服务器地址: http://localhost:8080
    echo 按 Ctrl+C 停止服务器
    echo.
    python -m http.server 8080
    goto :end
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo 检测到Node.js环境，检查http-server...
    npx http-server --version >nul 2>&1
    if %errorlevel% == 0 (
        echo 启动HTTP服务器...
        echo 服务器地址: http://localhost:8080
        echo 按 Ctrl+C 停止服务器
        echo.
        npx http-server -p 8080
        goto :end
    ) else (
        echo 正在安装http-server...
        npm install -g http-server
        if %errorlevel% == 0 (
            echo 启动HTTP服务器...
            echo 服务器地址: http://localhost:8080
            echo 按 Ctrl+C 停止服务器
            echo.
            http-server -p 8080
            goto :end
        )
    )
)

REM 如果没有Python或Node.js，提供其他选项
echo 未检测到Python或Node.js环境
echo.
echo 请选择以下选项之一：
echo 1. 直接在浏览器中打开 index.html
echo 2. 安装Python或Node.js后重新运行此脚本
echo 3. 配置IIS或其他Web服务器
echo.
echo 按任意键直接打开 index.html...
pause >nul
start index.html

:end
echo.
echo 服务器已停止
pause
