# 版本管理快速参考卡片

## 🚀 一分钟快速上手

### 查看当前版本
```bash
方法1: 打开 index.html → 查看页面底部
方法2: 打开 version.json 文件
方法3: 运行 scripts/版本检查.html
```

### 更新版本（3步骤）
```bash
1. 双击 scripts/版本更新.bat
2. 编辑 version.json 和 CHANGELOG.md  
3. 双击 scripts/打包发布.bat
```

## 📋 版本类型选择

| 更新类型 | 版本变化 | 使用场景 | 示例 |
|---------|---------|---------|------|
| **修订版本** | 1.0.0 → 1.0.1 | Bug修复、小优化 | 修复日期显示错误 |
| **次版本** | 1.0.0 → 1.1.0 | 新功能、功能增强 | 添加批量处理功能 |
| **主版本** | 1.0.0 → 2.0.0 | 重大更新、架构变更 | 全新界面设计 |

## 🔧 常用命令

### 版本更新脚本选项
```
1 = Bug修复 (PATCH)
2 = 新功能 (MINOR)  
3 = 重大更新 (MAJOR)
4 = 自定义版本号
```

### 文件编辑要点
```json
// version.json 必须更新的字段
{
  "version": "1.0.1",        // 新版本号
  "buildDate": "2024-12-20", // 当前日期
  "description": "..."       // 更新描述
}
```

```markdown
// CHANGELOG.md 格式
## [1.0.1] - 2024-12-20
### 修复
- 修复了...
### 新增  
- 添加了...
```

## ⚡ 5分钟发布流程

```
开始 → 运行更新脚本 → 编辑文件 → 验证检查 → 打包发布 → 完成
 1分钟     1分钟       2分钟      30秒      30秒     5分钟
```

### 详细步骤
1. **运行** `scripts/版本更新.bat` (选择版本类型)
2. **编辑** `version.json` (更新版本号和日期)
3. **编辑** `CHANGELOG.md` (添加变更记录)
4. **验证** `scripts/版本检查.html` (确认无误)
5. **打包** `scripts/打包发布.bat` (创建发布包)

## 🚨 常见错误避免

### ❌ 错误做法
- 忘记更新 CHANGELOG.md
- 版本号格式错误 (如: *******)
- 跳过测试验证步骤
- 不备份当前版本

### ✅ 正确做法
- 每次都更新变更日志
- 严格遵循语义化版本号
- 充分测试后再发布
- 发布前备份当前版本

## 📁 重要文件位置

```
项目根目录/
├── version.json           ← 版本配置文件
├── CHANGELOG.md          ← 变更日志
├── scripts/
│   ├── 版本更新.bat      ← 版本更新工具
│   ├── 打包发布.bat      ← 打包发布工具
│   └── 版本检查.html     ← 版本检查工具
└── releases/             ← 发布版本存档
    ├── v1.0.0/
    ├── v1.0.1/
    └── ...
```

## 🔍 问题诊断

### 版本信息不显示
```bash
检查: version.json 文件格式是否正确
解决: 验证JSON语法，确保无语法错误
```

### 脚本运行失败
```bash
检查: 文件权限和路径
解决: 右键→属性→确认可执行权限
```

### 打包失败
```bash
检查: 磁盘空间和文件完整性
解决: 清理空间，检查必要文件是否存在
```

## 📞 获取帮助

### 自助资源
- `版本管理使用手册.md` - 详细使用说明
- `版本管理演示.md` - 实际操作演示
- `scripts/版本检查.html` - 自动诊断工具

### 技术支持
- 📧 <EMAIL>
- 📱 400-xxx-xxxx

## ✅ 每次发布检查清单

**发布前确认：**
- [ ] 代码测试完成
- [ ] 版本号正确更新
- [ ] 变更日志已更新
- [ ] 版本检查通过
- [ ] 发布包创建成功

**发布后确认：**
- [ ] 部署成功
- [ ] 功能验证通过
- [ ] 版本信息正确显示
- [ ] 相关人员已通知

---

## 💡 记住这些要点

1. **版本号规则**: 主.次.修订 (如: 1.2.3)
2. **更新频率**: Bug修复随时，功能更新定期
3. **测试验证**: 每次发布前必须测试
4. **文档同步**: 代码和文档同步更新
5. **备份习惯**: 发布前备份当前版本

**版本管理是团队协作的基础，请严格按照流程执行！**
