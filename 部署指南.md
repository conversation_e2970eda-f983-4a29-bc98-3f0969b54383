# 菜篮子智能比价工具 - 内部网络部署指南

## 概述
本工具是一个纯前端应用，可以在内部网络环境中独立运行，无需外部网络连接。

## 部署前准备

### 1. 下载SheetJS库
由于内部网络无法访问CDN，需要手动下载SheetJS库：

1. 在有外网的环境中访问：https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js
2. 下载完整的JavaScript文件
3. 将下载的内容替换 `libs/xlsx.min.js` 文件中的内容

### 2. 文件结构
确保以下文件结构完整：
```
菜篮子智能比价工具/
├── index.html          # 主页面
├── styles.css          # 样式文件
├── script.js           # 主要逻辑
├── libs/
│   └── xlsx.min.js     # SheetJS库（需要替换为完整版本）
├── README.md           # 使用说明
└── 部署指南.md         # 本文件
```

## 部署步骤

### 方法一：Web服务器部署（推荐）

1. **安装Web服务器**
   - Windows: 可使用IIS、Apache、Nginx
   - Linux: 推荐使用Nginx或Apache

2. **配置Web服务器**
   - 将整个项目文件夹复制到Web服务器的根目录
   - 确保Web服务器可以访问所有文件
   - 配置默认页面为 `index.html`

3. **访问应用**
   - 通过浏览器访问：`http://服务器IP地址/菜篮子智能比价工具/`
   - 或配置域名：`http://your-domain.com/`

### 方法二：本地文件访问

1. **直接打开**
   - 双击 `index.html` 文件
   - 或在浏览器中打开文件路径

2. **注意事项**
   - 某些浏览器可能限制本地文件访问
   - 推荐使用Chrome、Firefox或Edge浏览器
   - 如遇到CORS错误，请使用Web服务器部署

### 方法三：简易HTTP服务器

如果有Python环境：
```bash
# Python 3
cd 菜篮子智能比价工具
python -m http.server 8080

# Python 2
python -m SimpleHTTPServer 8080
```

如果有Node.js环境：
```bash
# 安装http-server
npm install -g http-server

# 启动服务器
cd 菜篮子智能比价工具
http-server -p 8080
```

## 浏览器兼容性

### 支持的浏览器
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11（部分功能可能受限）

### 推荐配置
- 启用JavaScript
- 允许文件上传
- 建议使用最新版本浏览器

## 安全注意事项

1. **文件上传安全**
   - 工具仅在浏览器本地处理文件
   - 不会将文件上传到服务器
   - 所有数据处理都在客户端完成

2. **网络安全**
   - 无需外部网络连接
   - 不会发送数据到外部服务器
   - 适合内部敏感数据处理

## 故障排除

### 常见问题

1. **SheetJS库加载失败**
   - 检查 `libs/xlsx.min.js` 是否为完整的库文件
   - 确认文件路径正确
   - 查看浏览器控制台错误信息

2. **文件上传失败**
   - 确认文件格式为Excel (.xlsx, .xls)
   - 检查文件是否损坏
   - 尝试使用其他Excel文件测试

3. **页面显示异常**
   - 检查所有CSS和JS文件是否完整
   - 清除浏览器缓存
   - 尝试使用其他浏览器

4. **功能不正常**
   - 打开浏览器开发者工具（F12）
   - 查看Console标签页的错误信息
   - 检查Network标签页的资源加载情况

### 调试模式
在浏览器中按F12打开开发者工具，可以：
- 查看详细的处理日志
- 监控文件解析过程
- 诊断数据匹配问题

## 性能优化

1. **文件大小限制**
   - 建议单个Excel文件不超过10MB
   - 数据行数建议控制在10000行以内

2. **浏览器性能**
   - 关闭不必要的浏览器标签页
   - 确保足够的内存空间
   - 定期清理浏览器缓存

## 更新维护

1. **版本更新**
   - 替换相应的文件即可
   - 建议备份当前版本

2. **功能扩展**
   - 可根据需要修改 `script.js`
   - 样式调整可修改 `styles.css`

## 技术支持

如遇到技术问题，请：
1. 查看浏览器控制台错误信息
2. 检查文件完整性
3. 确认浏览器兼容性
4. 联系技术支持人员

---

**注意：部署前请确保已正确下载并替换SheetJS库文件，否则工具将无法正常工作。**
