<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本检查工具 - 菜篮子智能比价工具</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .version-info {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        .check-item {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .check-item.success {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .check-item.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .check-item.warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
        .status {
            font-weight: bold;
            margin-right: 10px;
            min-width: 60px;
        }
        .success .status { color: #28a745; }
        .error .status { color: #dc3545; }
        .warning .status { color: #ffc107; }
        .description {
            flex: 1;
        }
        .actions {
            margin-top: 30px;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .version-details {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .file-list {
            margin-top: 15px;
            padding: 10px;
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 版本检查工具</h1>
        
        <div class="version-info" id="versionInfo">
            <h3>📋 当前版本信息</h3>
            <div id="versionDetails">正在加载版本信息...</div>
        </div>
        
        <div id="results">
            <div class="check-item" id="check-version">
                <span class="status">检查中...</span>
                <span class="description">检查版本文件是否存在</span>
            </div>
            
            <div class="check-item" id="check-files">
                <span class="status">检查中...</span>
                <span class="description">检查核心文件完整性</span>
            </div>
            
            <div class="check-item" id="check-dependencies">
                <span class="status">检查中...</span>
                <span class="description">检查依赖库版本</span>
            </div>
            
            <div class="check-item" id="check-compatibility">
                <span class="status">检查中...</span>
                <span class="description">检查浏览器兼容性</span>
            </div>
        </div>
        
        <div class="file-list" id="fileList" style="display:none;">
            <h4>📁 文件清单</h4>
            <div id="fileDetails"></div>
        </div>
        
        <div class="actions">
            <button class="btn" onclick="runChecks()">重新检查</button>
            <button class="btn" onclick="showFileList()">显示文件清单</button>
            <a href="../index.html" class="btn" style="background-color: #28a745;">返回主应用</a>
        </div>
    </div>

    <script>
        let versionData = null;

        function updateCheckItem(id, status, message) {
            const item = document.getElementById(id);
            const statusSpan = item.querySelector('.status');
            const descSpan = item.querySelector('.description');
            
            item.className = 'check-item ' + status;
            statusSpan.textContent = status === 'success' ? '✓ 通过' : 
                                   status === 'error' ? '✗ 失败' : 
                                   '⚠ 警告';
            descSpan.textContent = message;
        }

        async function loadVersionInfo() {
            try {
                const response = await fetch('../version.json');
                if (!response.ok) throw new Error('版本文件不存在');
                
                versionData = await response.json();
                
                const versionDetails = document.getElementById('versionDetails');
                versionDetails.innerHTML = `
                    <strong>版本号：</strong>${versionData.version}<br>
                    <strong>构建日期：</strong>${versionData.buildDate}<br>
                    <strong>构建编号：</strong>${versionData.buildNumber}<br>
                    <strong>发布类型：</strong>${versionData.releaseType}<br>
                    <strong>描述：</strong>${versionData.description}
                `;
                
                updateCheckItem('check-version', 'success', '版本文件加载成功');
                return true;
            } catch (error) {
                updateCheckItem('check-version', 'error', '版本文件加载失败：' + error.message);
                return false;
            }
        }

        async function checkFiles() {
            const requiredFiles = [
                '../index.html',
                '../styles.css', 
                '../script.js',
                '../libs/xlsx.min.js'
            ];
            
            let missingFiles = [];
            
            for (const file of requiredFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (!response.ok) {
                        missingFiles.push(file.replace('../', ''));
                    }
                } catch (error) {
                    missingFiles.push(file.replace('../', ''));
                }
            }
            
            if (missingFiles.length === 0) {
                updateCheckItem('check-files', 'success', '所有核心文件完整');
                return true;
            } else {
                updateCheckItem('check-files', 'error', 
                    '缺少文件：' + missingFiles.join(', '));
                return false;
            }
        }

        function checkDependencies() {
            try {
                // 检查SheetJS库
                if (typeof XLSX !== 'undefined' && XLSX.read && XLSX.utils) {
                    updateCheckItem('check-dependencies', 'success', 
                        'SheetJS库已正确加载');
                    return true;
                } else {
                    updateCheckItem('check-dependencies', 'warning', 
                        'SheetJS库未加载或不完整，请检查libs/xlsx.min.js文件');
                    return false;
                }
            } catch (error) {
                updateCheckItem('check-dependencies', 'error', 
                    '依赖检查失败：' + error.message);
                return false;
            }
        }

        function checkCompatibility() {
            const features = {
                fileAPI: typeof FileReader !== 'undefined',
                arrayBuffer: typeof ArrayBuffer !== 'undefined',
                fetch: typeof fetch !== 'undefined',
                promise: typeof Promise !== 'undefined',
                localStorage: typeof localStorage !== 'undefined'
            };
            
            const missing = Object.keys(features).filter(key => !features[key]);
            
            if (missing.length === 0) {
                updateCheckItem('check-compatibility', 'success', 
                    '浏览器完全兼容，支持所有必要功能');
                return true;
            } else if (missing.length <= 2) {
                updateCheckItem('check-compatibility', 'warning', 
                    '浏览器基本兼容，但缺少部分功能：' + missing.join(', '));
                return true;
            } else {
                updateCheckItem('check-compatibility', 'error', 
                    '浏览器不兼容，缺少关键功能：' + missing.join(', '));
                return false;
            }
        }

        function showFileList() {
            const fileList = document.getElementById('fileList');
            const fileDetails = document.getElementById('fileDetails');
            
            if (versionData && versionData.features) {
                fileDetails.innerHTML = `
                    <h5>核心功能：</h5>
                    <ul>
                        ${versionData.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    <h5>系统要求：</h5>
                    <p><strong>浏览器：</strong>${versionData.requirements.browser}</p>
                    <p><strong>JavaScript：</strong>${versionData.requirements.javascript}</p>
                    <h5>依赖库：</h5>
                    <ul>
                        ${Object.entries(versionData.requirements.dependencies).map(
                            ([name, version]) => `<li>${name}: ${version}</li>`
                        ).join('')}
                    </ul>
                `;
            } else {
                fileDetails.innerHTML = '<p>版本信息未加载，无法显示详细信息</p>';
            }
            
            fileList.style.display = fileList.style.display === 'none' ? 'block' : 'none';
        }

        async function runChecks() {
            // 重置所有检查项
            const checkItems = ['check-version', 'check-files', 'check-dependencies', 'check-compatibility'];
            checkItems.forEach(id => {
                updateCheckItem(id, '', '检查中...');
            });

            // 依次执行检查
            await loadVersionInfo();
            await checkFiles();
            checkDependencies();
            checkCompatibility();
        }

        // 页面加载完成后自动运行检查
        window.addEventListener('load', runChecks);
    </script>
</body>
</html>
