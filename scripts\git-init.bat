@echo off
echo ========================================
echo Git Init Tool
echo ========================================
echo.

git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Git not installed
    echo Please install Git from https://git-scm.com/
    pause
    exit /b 1
)

echo Git is installed
echo.

if exist "../.git" (
    echo WARNING: Already a Git repository
    pause
    exit /b 0
)

echo Initializing Git repository...
cd ..
git init

echo.
echo Please enter your information:
set /p username="Your name: "
set /p email="Your email: "

git config user.name "%username%"
git config user.email "%email%"

echo.
echo Adding files...
git add .

echo Creating initial commit...
git commit -m "feat: initial commit v1.0.0"

echo Creating version tag...
git tag -a v1.0.0 -m "Initial release v1.0.0"

echo.
echo ========================================
echo Git Setup Complete!
echo ========================================
echo.
echo Status:
git status --short
echo.
echo Commands:
echo git status
echo git log --oneline
echo git tag
echo.

pause
