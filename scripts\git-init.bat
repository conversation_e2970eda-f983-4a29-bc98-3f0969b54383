@echo off
echo ========================================
echo Git Repository Initialization Tool
echo ========================================
echo.

REM Check if Git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/
    pause
    exit /b 1
)

echo [OK] Git is installed
echo.

REM Check if already a Git repository
if exist ".git" (
    echo [WARNING] This directory is already a Git repository
    set /p continue="Do you want to continue? (y/n): "
    if /i not "%continue%"=="y" (
        echo Operation cancelled
        pause
        exit /b 0
    )
) else (
    echo Initializing Git repository...
    git init
    echo [OK] Git repository initialized
)

echo.
echo Setting up Git configuration...

REM Get user information
set /p username="Enter your name: "
set /p email="Enter your email: "

REM Configure Git
git config user.name "%username%"
git config user.email "%email%"

echo [OK] Git configuration completed
echo.

REM Create initial commit
echo Creating initial commit...

REM Add all files
git add .

REM Create initial commit
git commit -m "feat: initial commit - 菜篮子智能比价工具 v1.0.0"

if %errorlevel% == 0 (
    echo [OK] Initial commit created
) else (
    echo [WARNING] Commit failed or no changes to commit
)

REM Create initial tag
echo Creating initial version tag...
git tag -a v1.0.0 -m "Release version 1.0.0 - Initial release"

if %errorlevel% == 0 (
    echo [OK] Version tag v1.0.0 created
) else (
    echo [WARNING] Tag creation failed
)

echo.
echo ========================================
echo Git Repository Setup Complete!
echo ========================================
echo.
echo Repository Status:
git status --short
echo.
echo Available Commands:
echo - git status          : Check repository status
echo - git log --oneline   : View commit history
echo - git tag             : List all tags
echo.
echo Next Steps:
echo 1. Use 'git-commit.bat' for daily commits
echo 2. Use 'git-release.bat' for version releases
echo 3. Read 'Git版本管理指南.md' for detailed usage
echo.

pause
