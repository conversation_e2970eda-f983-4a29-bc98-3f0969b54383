@echo off
echo ========================================
echo Price Comparison Tool - Local Server
echo ========================================
echo.

python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting Python HTTP server...
    echo Open browser and go to: http://localhost:8080
    echo Press Ctrl+C to stop
    echo.
    python -m http.server 8080
    goto end
)

node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting Node.js HTTP server...
    echo Open browser and go to: http://localhost:8080
    echo Press Ctrl+C to stop
    echo.
    npx http-server -p 8080
    goto end
)

echo No Python or Node.js found
echo Opening index.html directly...
start index.html

:end
pause
