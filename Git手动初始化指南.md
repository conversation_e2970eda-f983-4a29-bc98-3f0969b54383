# Git手动初始化指南

## 🚨 批处理文件编码问题解决方案

由于Windows环境下批处理文件的编码问题，推荐使用手动命令进行Git初始化。

## 🚀 手动初始化Git仓库（推荐）

### 步骤1：打开命令行

```bash
# 方法1：在项目目录右键选择"在此处打开PowerShell窗口"
# 方法2：Win+R 输入 cmd，然后 cd 到项目目录
cd D:\菜篮子智能比价工具
```

### 步骤2：检查Git安装

```bash
git --version
# 应该显示：git version 2.x.x.windows.x
```

如果显示"git不是内部或外部命令"，请先安装Git：
- 下载地址：https://git-scm.com/
- 安装后重启命令行

### 步骤3：初始化Git仓库

```bash
# 初始化Git仓库
git init

# 配置用户信息（替换为您的信息）
git config user.name "您的姓名"
git config user.email "您的邮箱@example.com"

# 查看配置
git config --list
```

### 步骤4：创建首次提交

```bash
# 添加所有文件到暂存区
git add .

# 创建首次提交
git commit -m "feat: initial commit - 菜篮子智能比价工具 v1.0.0"

# 创建版本标签
git tag -a v1.0.0 -m "Initial release v1.0.0"
```

### 步骤5：验证设置

```bash
# 查看仓库状态
git status

# 查看提交历史
git log --oneline

# 查看标签
git tag

# 查看分支
git branch
```

## ✅ 成功标志

如果看到以下输出，说明Git仓库初始化成功：

```bash
PS D:\菜篮子智能比价工具> git status
On branch main
nothing to commit, working tree clean

PS D:\菜篮子智能比价工具> git log --oneline
a1b2c3d (HEAD -> main, tag: v1.0.0) feat: initial commit - 菜篮子智能比价工具 v1.0.0

PS D:\菜篮子智能比价工具> git tag
v1.0.0
```

## 📋 日常Git操作

### 提交变更

```bash
# 查看当前状态
git status

# 查看具体变更
git diff

# 添加变更到暂存区
git add .

# 提交变更
git commit -m "fix: 修复日期匹配算法Bug"
```

### 创建版本标签

```bash
# 创建新版本标签
git tag -a v1.0.1 -m "Release v1.0.1 - 修复日期匹配Bug"

# 查看所有标签
git tag

# 查看特定标签信息
git show v1.0.1
```

### 版本回滚

```bash
# 查看提交历史
git log --oneline

# 回滚到特定版本（临时）
git checkout v1.0.0

# 回到最新版本
git checkout main

# 永久回滚（谨慎使用）
git reset --hard v1.0.0
```

## 🔧 常用Git命令速查

### 基础操作
```bash
git status              # 查看仓库状态
git add .               # 添加所有变更
git add 文件名          # 添加特定文件
git commit -m "消息"    # 提交变更
git log                 # 查看提交历史
git log --oneline       # 简洁的提交历史
git diff                # 查看变更差异
```

### 标签管理
```bash
git tag                 # 列出所有标签
git tag v1.0.1          # 创建轻量标签
git tag -a v1.0.1 -m "说明"  # 创建注释标签
git show v1.0.1         # 查看标签信息
git tag -d v1.0.1       # 删除标签
```

### 分支操作
```bash
git branch              # 查看分支
git branch 分支名       # 创建分支
git checkout 分支名     # 切换分支
git checkout -b 分支名  # 创建并切换分支
git merge 分支名        # 合并分支
git branch -d 分支名    # 删除分支
```

## 🎯 提交信息规范

### 提交类型
- `feat:` 新功能
- `fix:` Bug修复
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建工具或辅助工具

### 示例
```bash
git commit -m "feat: 添加批量数据处理功能"
git commit -m "fix: 修复Excel文件上传失败问题"
git commit -m "docs: 更新用户使用手册"
git commit -m "style: 调整代码格式和缩进"
git commit -m "refactor: 重构日期匹配算法"
```

## 🔄 版本发布流程

### 完整发布流程
```bash
# 1. 确保所有变更已提交
git status

# 2. 更新版本信息（手动编辑version.json）
# 编辑 version.json 文件，更新版本号和日期

# 3. 提交版本更新
git add version.json
git commit -m "release: v1.0.1 - 修复日期匹配Bug"

# 4. 创建版本标签
git tag -a v1.0.1 -m "Release v1.0.1: 修复日期匹配Bug"

# 5. 创建发布包（可选）
git archive --format=zip --output=releases/v1.0.1.zip v1.0.1
```

## 🚨 故障排除

### 问题1：中文乱码
```bash
# 设置Git编码
git config --global core.quotepath false
git config --global gui.encoding utf-8
git config --global i18n.commit.encoding utf-8
git config --global i18n.logoutputencoding utf-8
```

### 问题2：提交失败
```bash
# 检查是否有文件变更
git status

# 如果没有变更，无需提交
# 如果有变更但提交失败，检查提交信息格式
```

### 问题3：标签已存在
```bash
# 删除现有标签
git tag -d v1.0.1

# 重新创建标签
git tag -a v1.0.1 -m "新的标签说明"
```

## 📞 获取帮助

### Git帮助命令
```bash
git help                # Git帮助
git help commit         # 特定命令帮助
git --help              # 命令行帮助
```

### 在线资源
- Git官方文档：https://git-scm.com/docs
- Git教程：https://www.atlassian.com/git/tutorials
- Git速查表：https://training.github.com/downloads/zh_CN/github-git-cheat-sheet/

## ✅ 下一步

完成Git初始化后，您可以：

1. **日常使用**：使用上述命令进行日常的代码提交和版本管理
2. **学习进阶**：阅读`Git版本管理指南.md`了解更多高级功能
3. **团队协作**：考虑使用GitHub、GitLab等远程仓库服务
4. **工具集成**：使用VS Code、SourceTree等图形化Git工具

**记住：Git是一个强大的工具，掌握基础操作后会大大提升您的开发效率！**
